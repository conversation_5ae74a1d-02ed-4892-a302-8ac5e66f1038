import { useState } from "react";
import { AdminHeader } from "./AdminHeader";
import { AdminSidebar } from "./AdminSidebar";
import { AdminBreadcrumb } from "./AdminBreadcrumb";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
      {/* Admin Sidebar */}
      <AdminSidebar isOpen={isSidebarOpen} onClose={closeSidebar} />

      {/* Main content area */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden lg:ml-0">
        {/* Admin Header */}
        <AdminHeader onMenuClick={toggleSidebar} />

        {/* Page content with breadcrumb */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          {/* Breadcrumb Navigation */}
          <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <AdminBreadcrumb />
            </div>
          </div>

          {/* Main content */}
          <div className="bg-gray-50 dark:bg-dark-900 min-h-full transition-theme duration-theme">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
