import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";

export function StorageManagement() {
  usePageTitle("Storage Management - Admin");

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
            Storage Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
            This comprehensive storage analytics interface will be implemented
            in Stage 8 of the development plan.
          </p>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 transition-theme duration-theme">
            <p className="text-sm text-orange-700 dark:text-orange-300 transition-theme duration-theme">
              🚀 Coming soon: Storage analytics, cleanup tools, quota
              management, and more!
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
