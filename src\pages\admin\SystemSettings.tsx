import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";

export function SystemSettings() {
  usePageTitle("System Settings - Admin");

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
          <div className="w-16 h-16 bg-gradient-to-br from-gray-500 to-slate-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
            System Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
            This comprehensive system configuration interface will be
            implemented in Stage 10 of the development plan.
          </p>
          <div className="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4 transition-theme duration-theme">
            <p className="text-sm text-gray-700 dark:text-gray-300 transition-theme duration-theme">
              🚀 Coming soon: System configuration, maintenance tools, backup
              management, and more!
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
