import React from "react";
import { cn } from "../../lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  color?: "white" | "primary" | "gray";
  className?: string;
}

export function LoadingSpinner({ 
  size = "md", 
  color = "white", 
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5", 
    lg: "h-6 w-6"
  };

  const colorClasses = {
    white: "text-white",
    primary: "text-primary-600 dark:text-primary-400",
    gray: "text-gray-600 dark:text-gray-400"
  };

  return (
    <svg
      className={cn(
        "animate-spin",
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
}

interface LoadingButtonProps {
  loading: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  loadingText: string;
  className?: string;
  type?: "button" | "submit" | "reset";
  onClick?: () => void;
  variant?: "primary" | "secondary";
}

export function LoadingButton({
  loading,
  disabled = false,
  children,
  loadingText,
  className,
  type = "button",
  onClick,
  variant = "primary"
}: LoadingButtonProps) {
  const isDisabled = loading || disabled;

  const baseClasses = "relative flex items-center justify-center w-full transition-all duration-200 font-medium rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-offset-2 min-h-[48px]";
  
  const variantClasses = {
    primary: "bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600 dark:focus:ring-primary-400",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100 dark:focus:ring-gray-400"
  };

  const disabledClasses = "opacity-60 cursor-not-allowed";

  return (
    <button
      type={type}
      disabled={isDisabled}
      onClick={onClick}
      className={cn(
        baseClasses,
        variantClasses[variant],
        isDisabled && disabledClasses,
        className
      )}
    >
      {loading ? (
        <>
          <LoadingSpinner size="md" color="white" className="mr-3" />
          <span className="select-none">{loadingText}</span>
        </>
      ) : (
        children
      )}
    </button>
  );
}

// Pulse animation for skeleton loading
export function PulseLoader({ className }: { className?: string }) {
  return (
    <div className={cn("animate-pulse bg-gray-200 dark:bg-gray-700 rounded", className)} />
  );
}

// Dots loading animation
export function DotsLoader({ className }: { className?: string }) {
  return (
    <div className={cn("flex space-x-1", className)}>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "0ms" }} />
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "150ms" }} />
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "300ms" }} />
    </div>
  );
}

// Progress bar loader
interface ProgressLoaderProps {
  progress: number; // 0-100
  className?: string;
  showPercentage?: boolean;
}

export function ProgressLoader({ 
  progress, 
  className, 
  showPercentage = false 
}: ProgressLoaderProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between items-center mb-1">
        {showPercentage && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {Math.round(progress)}%
          </span>
        )}
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="bg-primary-600 dark:bg-primary-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
}
