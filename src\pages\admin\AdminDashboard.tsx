import { useState, useEffect } from "react";
import { usePageTitle } from "../../hooks/usePageTitle";
import { useAuth } from "../../contexts/AuthContext";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";
import { adminService } from "../../lib/admin/adminService";
import { formatFileSize, formatPercentage } from "../../lib/admin/adminUtils";
import type { SystemStats } from "../../types/admin";

export function AdminDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Set page title
  usePageTitle("Admin Dashboard");

  // Load system statistics
  useEffect(() => {
    const loadStats = async (showLoader = true) => {
      try {
        if (showLoader) {
          setLoading(true);
        }
        const systemStats = await adminService.getSystemStats();
        setStats(systemStats);
        setError(null);
        setLastUpdated(new Date());
      } catch (err: any) {
        setError(err.message || "Failed to load system statistics");
      } finally {
        if (showLoader) {
          setLoading(false);
        }
      }
    };

    // Initial load with loader
    loadStats(true);

    // Background refresh every 60 seconds without loader
    const interval = setInterval(() => loadStats(false), 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex md:flex-row flex-col items-start md:items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    Admin Dashboard
                  </h1>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Welcome back, {user?.profile?.full_name || "Administrator"}!
                    👋
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  {lastUpdated && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                      <span className="hidden sm:inline">Last updated: </span>
                      <span className="font-medium">
                        {lastUpdated.toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </span>
                    </div>
                  )}

                  <button
                    onClick={() => {
                      const loadStats = async () => {
                        try {
                          const systemStats =
                            await adminService.getSystemStats();
                          setStats(systemStats);
                          setError(null);
                          setLastUpdated(new Date());
                        } catch (err: any) {
                          setError(
                            err.message || "Failed to load system statistics"
                          );
                        }
                      };
                      loadStats();
                    }}
                    className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-all duration-200"
                    title="Refresh data"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    <span className="hidden sm:inline">Refresh</span>
                  </button>

                  <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium transition-theme duration-theme">
                    System Online
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
            Welcome back, {user?.profile?.full_name || "Administrator"}! 👋
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Here's what's happening with your BrimBag platform today.
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
              Loading system statistics...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8 transition-theme duration-theme">
            <div className="flex">
              <svg
                className="w-5 h-5 text-red-400 mr-3 mt-0.5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 transition-theme duration-theme">
                  Error loading statistics
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1 transition-theme duration-theme">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Statistics Cards */}
        {stats && !loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Users Card */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm p-6 border border-gray-200 dark:border-dark-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Total Users
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {stats.users.total_users.toLocaleString()}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400 transition-theme duration-theme">
                    +{stats.users.new_users_30d} this month
                  </p>
                </div>
              </div>
            </div>

            {/* Documents Card */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm p-6 border border-gray-200 dark:border-dark-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Documents
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {stats.documents.total_documents.toLocaleString()}
                  </p>
                  <p className="text-xs text-blue-600 dark:text-blue-400 transition-theme duration-theme">
                    {formatFileSize(stats.documents.total_storage_used)} total
                  </p>
                </div>
              </div>
            </div>

            {/* Rooms Card */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm p-6 border border-gray-200 dark:border-dark-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Total Rooms
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {stats.rooms.total_rooms.toLocaleString()}
                  </p>
                  <p className="text-xs text-purple-600 dark:text-purple-400 transition-theme duration-theme">
                    {stats.rooms.public_rooms} public,{" "}
                    {stats.rooms.private_rooms} private
                  </p>
                </div>
              </div>
            </div>

            {/* Storage Card */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm p-6 border border-gray-200 dark:border-dark-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Storage Used
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {formatPercentage(
                      stats.storage.total_used,
                      stats.storage.total_capacity
                    )}
                  </p>
                  <p className="text-xs text-orange-600 dark:text-orange-400 transition-theme duration-theme">
                    {formatFileSize(stats.storage.total_used)} of{" "}
                    {formatFileSize(stats.storage.total_capacity)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions and System Health */}
        {stats && !loading && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Quick Actions */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6 transition-theme duration-theme">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-theme duration-theme">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button className="flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-200 group">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                        Manage Users
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        View and edit user accounts
                      </p>
                    </div>
                  </button>

                  <button className="flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg hover:from-green-100 hover:to-emerald-100 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 transition-all duration-200 group">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                        View Documents
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Monitor file uploads and sharing
                      </p>
                    </div>
                  </button>

                  <button className="flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg hover:from-purple-100 hover:to-violet-100 dark:hover:from-purple-900/30 dark:hover:to-violet-900/30 transition-all duration-200 group">
                    <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                        View Activity
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Monitor system activity logs
                      </p>
                    </div>
                  </button>

                  <button className="flex items-center p-4 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg hover:from-orange-100 hover:to-amber-100 dark:hover:from-orange-900/30 dark:hover:to-amber-900/30 transition-all duration-200 group">
                    <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                        System Settings
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                        Configure system parameters
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            {/* System Health */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6 transition-theme duration-theme">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-theme duration-theme">
                  System Health
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Storage Usage
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.storage.usage_percentage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2 transition-theme duration-theme">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500"
                      style={{
                        width: `${Math.min(
                          stats.storage.usage_percentage,
                          100
                        )}%`,
                      }}
                    ></div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Active Users
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.users.total_users}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      Total Rooms
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {stats.rooms.total_rooms}
                    </span>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-dark-700 transition-theme duration-theme">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium text-green-600 dark:text-green-400 transition-theme duration-theme">
                        All Systems Operational
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
