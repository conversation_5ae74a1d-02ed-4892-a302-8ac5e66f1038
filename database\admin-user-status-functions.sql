-- Add active column to profiles table if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT true;

-- Update existing users to be active by default
UPDATE profiles SET active = true WHERE active IS NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles(active);

-- Drop existing function if it exists (to handle return type changes)
DROP FUNCTION IF EXISTS admin_update_user_status(UUID, BOOLEAN, TEXT);

-- Function to update user active status (admin only)
CREATE OR REPLACE FUNCTION admin_update_user_status(
  p_user_id UUID,
  p_active BOOLEAN,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_admin_id UUID;
  v_user_record RECORD;
  v_result JSON;
BEGIN
  -- Get the current admin user ID
  v_admin_id := auth.uid();
  
  -- Verify admin access
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = v_admin_id 
    AND email = '<EMAIL>'
  ) THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;
  
  -- Get user record before update
  SELECT * INTO v_user_record FROM profiles WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Update user status (bypasses RLS)
  UPDATE profiles 
  SET 
    active = p_active,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Log the action
  INSERT INTO activity_logs (
    user_id,
    action_type,
    target_type,
    target_id,
    details
  ) VALUES (
    v_admin_id,
    CASE WHEN p_active THEN 'user_activate' ELSE 'user_deactivate' END,
    'user',
    p_user_id,
    jsonb_build_object(
      'target_user_email', v_user_record.email,
      'target_user_name', v_user_record.full_name,
      'previous_status', v_user_record.active,
      'new_status', p_active,
      'reason', COALESCE(p_reason, 'No reason provided'),
      'timestamp', NOW()
    )
  );
  
  -- Return success result
  v_result := json_build_object(
    'success', true,
    'message', CASE WHEN p_active THEN 'User activated successfully' ELSE 'User deactivated successfully' END,
    'user_id', p_user_id,
    'new_status', p_active
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Return error result
    v_result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'code', SQLSTATE
    );
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admin check is inside function)
GRANT EXECUTE ON FUNCTION admin_update_user_status(UUID, BOOLEAN, TEXT) TO authenticated;

-- Create trigger to handle real-time user status changes
CREATE OR REPLACE FUNCTION notify_user_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- If user is being deactivated, notify via realtime
  IF NEW.active = false AND OLD.active = true THEN
    PERFORM pg_notify(
      'user_status_changed',
      json_build_object(
        'user_id', NEW.id,
        'active', NEW.active,
        'event', 'deactivated'
      )::text
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user status changes
DROP TRIGGER IF EXISTS trigger_user_status_change ON profiles;
CREATE TRIGGER trigger_user_status_change
  AFTER UPDATE OF active ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION notify_user_status_change();
