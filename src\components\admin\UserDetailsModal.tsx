import { useState, useEffect } from "react";
import { adminService } from "../../lib/admin/adminService";
import { activityService } from "../../lib/admin/activityService";
import { formatFileSize } from "../../lib/admin/adminUtils";
import type { AdminUser, ActivityEvent } from "../../types/admin";

interface UserDetailsModalProps {
  user: AdminUser | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdate?: () => void;
}

export function UserDetailsModal({
  user,
  isOpen,
  onClose,
}: UserDetailsModalProps) {
  const [userDetails, setUserDetails] = useState<AdminUser | null>(null);
  const [userActivity, setUserActivity] = useState<ActivityEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"overview" | "activity">(
    "overview"
  );

  // Load detailed user information
  const loadUserDetails = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const [details, activity] = await Promise.all([
        adminService.getUserDetails(user.id),
        activityService.getUserActivityLogs(user.id, 1, 10),
      ]);

      setUserDetails(details);
      setUserActivity(activity.data);
    } catch (err: any) {
      setError(err.message || "Failed to load user details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && user) {
      loadUserDetails();
    }
  }, [isOpen, user]);

  if (!isOpen || !user) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getActionTypeColor = (actionType: string) => {
    switch (actionType) {
      case "login":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "logout":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
      case "upload":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "share":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300";
      case "delete_document":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative bg-white dark:bg-dark-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden transition-theme duration-theme">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-lg">
                  {user.full_name?.charAt(0) ||
                    user.email.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  {user.full_name || "No name provided"}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-colors duration-200"
            >
              <svg
                className="w-6 h-6 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-dark-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab("overview")}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === "overview"
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab("activity")}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === "activity"
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                Recent Activity
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <p className="text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}

            {!loading && !error && activeTab === "overview" && (
              <div className="space-y-6">
                {/* Account Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Account Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Email
                      </label>
                      <p className="text-gray-900 dark:text-gray-100">
                        {user.email}
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Full Name
                      </label>
                      <p className="text-gray-900 dark:text-gray-100">
                        {user.full_name || "Not provided"}
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Registration Date
                      </label>
                      <p className="text-gray-900 dark:text-gray-100">
                        {formatDate(user.created_at)}
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Account Status
                      </label>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                        Active
                      </span>
                    </div>
                  </div>
                </div>

                {/* Statistics */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Usage Statistics
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {userDetails?.user_stats.document_count ||
                          user.user_stats.document_count}
                      </div>
                      <div className="text-sm text-blue-600 dark:text-blue-400">
                        Documents
                      </div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatFileSize(
                          userDetails?.user_stats.storage_used ||
                            user.user_stats.storage_used
                        )}
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400">
                        Storage Used
                      </div>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {userDetails?.user_stats.rooms_joined ||
                          user.user_stats.rooms_joined}
                      </div>
                      <div className="text-sm text-purple-600 dark:text-purple-400">
                        Rooms Joined
                      </div>
                    </div>
                    <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {userDetails?.user_stats.rooms_created ||
                          user.user_stats.rooms_created}
                      </div>
                      <div className="text-sm text-orange-600 dark:text-orange-400">
                        Rooms Created
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Stats */}
                {userDetails?.user_stats.documents_shared_with_user !==
                  undefined && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                      Additional Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                        <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Documents Shared With User
                        </label>
                        <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          {userDetails.user_stats.documents_shared_with_user}
                        </p>
                      </div>
                      <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                        <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Recent Uploads (7 days)
                        </label>
                        <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          {userDetails.user_stats.recent_uploads_7d || 0}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {!loading && !error && activeTab === "activity" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Recent Activity
                </h3>
                {userActivity.length > 0 ? (
                  <div className="space-y-3">
                    {userActivity.map((activity) => (
                      <div
                        key={activity.id}
                        className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-dark-700 rounded-lg"
                      >
                        <div className="flex-shrink-0">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActionTypeColor(
                              activity.action_type
                            )}`}
                          >
                            {activity.action_type
                              .replace("_", " ")
                              .toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900 dark:text-gray-100">
                            {activity.details?.description ||
                              `Performed ${activity.action_type.replace(
                                "_",
                                " "
                              )}`}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(activity.created_at)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <svg
                      className="w-12 h-12 text-gray-400 mx-auto mb-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400">
                      No recent activity found
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
