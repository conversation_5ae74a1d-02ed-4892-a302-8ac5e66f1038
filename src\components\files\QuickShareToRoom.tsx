import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { roomService } from "../../lib/roomService";
import type { RoomWithDetails } from "../../lib/roomService";
import { useAuth } from "../../contexts/AuthContext";

const quickShareSchema = z.object({
  permission: z.enum(["view", "download"]),
});

type QuickShareFormData = z.infer<typeof quickShareSchema>;

interface QuickShareToRoomProps {
  document: DocumentFile;
  roomId: string;
  onClose: () => void;
  onShareComplete?: () => void;
}

export function QuickShareToRoom({
  document,
  roomId,
  onClose,
  onShareComplete,
}: QuickShareToRoomProps) {
  const { user } = useAuth();
  const [room, setRoom] = useState<RoomWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRoom, setIsLoadingRoom] = useState(true);

  const { register, handleSubmit } = useForm<QuickShareFormData>({
    defaultValues: {
      permission: "download",
    },
  });

  useEffect(() => {
    loadRoom();
  }, [roomId]);

  const loadRoom = async () => {
    if (!user) return;

    try {
      setIsLoadingRoom(true);
      const userRooms = await roomService.getUserRooms(user.id);
      const targetRoom = userRooms.find((r) => r.id === roomId);

      if (!targetRoom) {
        toast.error("Room not found or you don't have access");
        onClose();
        return;
      }

      setRoom(targetRoom);
    } catch (error: any) {
      console.error("Load room error:", error);
      toast.error("Failed to load room information");
      onClose();
    } finally {
      setIsLoadingRoom(false);
    }
  };

  const onSubmit = async (data: QuickShareFormData) => {
    if (!user || !room) {
      toast.error("Missing required information");
      return;
    }

    try {
      setIsLoading(true);

      await roomService.shareDocumentToRoom(
        document.id,
        roomId,
        user.id,
        data.permission
      );

      toast.success(`Document shared to ${room.name} successfully!`);
      onShareComplete?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to share document");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingRoom) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!room) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Share to Room
            </h3>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Document Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Document:
            </h4>
            <div className="flex items-center space-x-3">
              <div className="text-2xl">📄</div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {document.title}
                </p>
                <p className="text-xs text-gray-500">{document.file_type}</p>
              </div>
            </div>
          </div>

          {/* Room Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Sharing to:
            </h4>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">{room.name}</p>
                <p className="text-xs text-gray-500">
                  {room.member_count} members • {room.room_code}
                </p>
              </div>
              <span
                className={`px-2 py-1 text-xs font-medium rounded-full ${
                  room.user_role === "admin"
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {room.user_role}
              </span>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Permission */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Permission Level
              </label>
              <div className="space-y-3">
                <label className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    value="download"
                    {...register("permission")}
                    disabled={isLoading}
                    className="mt-0.5 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:opacity-50"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-4 h-4 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <span className="text-sm font-medium text-gray-900">
                        Download
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Members can view and download the document
                    </p>
                  </div>
                </label>

                <label className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    value="view"
                    {...register("permission")}
                    disabled={isLoading}
                    className="mt-0.5 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:opacity-50"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <svg
                        className="w-4 h-4 text-yellow-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      <span className="text-sm font-medium text-gray-900">
                        View Only
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Members can only view the document
                    </p>
                  </div>
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <span>{isLoading ? "Sharing..." : "Share Document"}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
