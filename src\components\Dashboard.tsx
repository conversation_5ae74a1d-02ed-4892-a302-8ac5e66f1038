import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "./layout/DashboardLayout";
import { fileService } from "../lib/fileService";
import { formatFileSize, formatDateTime } from "../lib/utils";
import { usePageTitle } from "../hooks/usePageTitle";

interface DashboardStats {
  totalDocuments: number;
  sharedFiles: number;
  activeRooms: number;
  storageUsed: number;
  recentFiles: Array<{
    id: string;
    title: string;
    file_type: string;
    file_size: number;
    created_at: string;
  }>;
}

export function Dashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Set page title
  usePageTitle("Dashboard");
  const [stats, setStats] = useState<DashboardStats>({
    totalDocuments: 0,
    sharedFiles: 0,
    activeRooms: 0,
    storageUsed: 0,
    recentFiles: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardStats();
    }
  }, [user]);

  const loadDashboardStats = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const dashboardStats = await fileService.getDashboardStatistics(user.id);
      setStats(dashboardStats);
    } catch (error) {
      console.error("Failed to load dashboard statistics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes("pdf")) return "📄";
    if (fileType.includes("word") || fileType.includes("document")) return "📝";
    if (fileType.includes("sheet") || fileType.includes("excel")) return "📊";
    if (fileType.includes("presentation") || fileType.includes("powerpoint"))
      return "📋";
    if (fileType.includes("image")) return "🖼️";
    if (fileType.includes("text")) return "📄";
    return "📄";
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Welcome section */}
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  Welcome back,{" "}
                  {user?.profile?.full_name?.split(" ")[0] || "there"}! 👋
                </h1>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                  Here's what's happening with your documents today.
                </p>
              </div>

              {/* Feedback CTA */}
              <div className="mt-4 lg:mt-0 lg:ml-6">
                <button
                  onClick={() => navigate("/feedback")}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700"
                >
                  <span className="mr-2">💬</span>
                  <span>Share Feedback</span>
                </button>
              </div>
            </div>
          </div>

          {/* Stats cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Documents Card */}
            <button
              onClick={() => navigate("/documents")}
              className="card p-6 hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer group min-h-[44px]"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center transition-theme duration-theme group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50">
                    <svg
                      className="w-5 h-5 text-blue-600 dark:text-blue-400 transition-theme duration-theme"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 text-left">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Total Documents
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {isLoading ? "..." : stats.totalDocuments}
                  </p>
                </div>
              </div>
            </button>

            {/* Shared Documents Card */}
            <button
              onClick={() => navigate("/shared")}
              className="card p-6 hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer group min-h-[44px]"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center transition-theme duration-theme group-hover:bg-green-200 dark:group-hover:bg-green-900/50">
                    <svg
                      className="w-5 h-5 text-green-600 dark:text-green-400 transition-theme duration-theme"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 text-left">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Shared Documents
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {isLoading ? "..." : stats.sharedFiles}
                  </p>
                </div>
              </div>
            </button>

            {/* Rooms Card */}
            <button
              onClick={() => navigate("/rooms")}
              className="card p-6 hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer group min-h-[44px]"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center transition-theme duration-theme group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50">
                    <svg
                      className="w-5 h-5 text-purple-600 dark:text-purple-400 transition-theme duration-theme"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 text-left">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Rooms
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {isLoading ? "..." : stats.activeRooms}
                  </p>
                </div>
              </div>
            </button>

            {/* Storage Used Card */}
            <button
              onClick={() => navigate("/documents")}
              className="card p-6 hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer group min-h-[44px]"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center transition-theme duration-theme group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50">
                    <svg
                      className="w-5 h-5 text-orange-600 dark:text-orange-400 transition-theme duration-theme"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4 text-left">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Storage Used
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                    {isLoading ? "..." : formatFileSize(stats.storageUsed)}
                  </p>
                </div>
              </div>
            </button>
          </div>

          {/* Recent activity and quick actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent files */}
            <div className="card">
              <div className="p-6 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  Recent Files
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {isLoading ? (
                    <div className="text-center py-4">
                      <div className="text-gray-500">
                        Loading recent files...
                      </div>
                    </div>
                  ) : stats.recentFiles.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-gray-500">No files uploaded yet</div>
                    </div>
                  ) : (
                    stats.recentFiles.map((file) => (
                      <div
                        key={file.id}
                        className="flex items-center justify-between py-2"
                      >
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-gray-100 dark:bg-dark-700 rounded-lg flex items-center justify-center transition-theme duration-theme">
                              <span className="text-xs font-medium text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                                {getFileIcon(file.file_type)}
                              </span>
                            </div>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                              {file.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                              {formatFileSize(file.file_size)} •{" "}
                              {formatDateTime(file.created_at)}
                            </p>
                          </div>
                        </div>
                        <button className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400 transition-theme duration-theme">
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                            />
                          </svg>
                        </button>
                      </div>
                    ))
                  )}
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => navigate("/documents")}
                    className="text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors duration-200"
                  >
                    View all files →
                  </button>
                </div>
              </div>
            </div>

            {/* Quick actions */}
            <div className="card">
              <div className="p-6 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                  Quick Actions
                </h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => navigate("/upload")}
                    className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
                  >
                    <svg
                      className="w-8 h-8 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      Upload File
                    </span>
                  </button>

                  <button
                    onClick={() => navigate("/rooms")}
                    className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
                  >
                    <svg
                      className="w-8 h-8 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      Create Room
                    </span>
                  </button>

                  <button
                    onClick={() => navigate("/documents")}
                    className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
                  >
                    <svg
                      className="w-8 h-8 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      Search Files
                    </span>
                  </button>

                  <button
                    onClick={() => navigate("/shared")}
                    className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
                  >
                    <svg
                      className="w-8 h-8 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      Shared Files
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
