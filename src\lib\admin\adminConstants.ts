// Admin constants for BrimBag
import type { AdminPermission } from "../../types/admin";

// Admin route paths
export const ADMIN_ROUTES = {
  ROOT: "/admin",
  DASHBOARD: "/admin/dashboard",
  USERS: "/admin/users",
  DOCUMENTS: "/admin/documents",
  ROOMS: "/admin/rooms",
  STORAGE: "/admin/storage",
  ACTIVITY: "/admin/activity",
  FEEDBACK: "/admin/feedback",
  SETTINGS: "/admin/settings",
} as const;

// Admin permissions
export const ADMIN_PERMISSIONS: Record<string, AdminPermission> = {
  VIEW_USERS: "view_users",
  MANAGE_USERS: "manage_users",
  VIEW_DOCUMENTS: "view_documents",
  MANAGE_DOCUMENTS: "manage_documents",
  VIEW_ROOMS: "view_rooms",
  MANAGE_ROOMS: "manage_rooms",
  VIEW_ANALYTICS: "view_analytics",
  VIEW_FEEDBACK: "view_feedback",
  MANAGE_FEEDBACK: "manage_feedback",
  SYSTEM_SETTINGS: "system_settings",
  VIEW_LOGS: "view_logs",
} as const;

// Admin action types
export const ADMIN_ACTION_TYPES = {
  USER_ROLE_CHANGE: "user_role_change",
  USER_DEACTIVATE: "user_deactivate",
  USER_ACTIVATE: "user_activate",
  DOCUMENT_DELETE: "document_delete",
  DOCUMENT_RESTORE: "document_restore",
  ROOM_ARCHIVE: "room_archive",
  ROOM_DELETE: "room_delete",
  ROOM_RESTORE: "room_restore",
  SYSTEM_SETTING_CHANGE: "system_setting_change",
  STORAGE_CLEANUP: "storage_cleanup",
  BULK_ACTION: "bulk_action",
  LOGIN: "admin_login",
  LOGOUT: "admin_logout",
} as const;

// Target types for admin actions
export const ADMIN_TARGET_TYPES = {
  USER: "user",
  DOCUMENT: "document",
  ROOM: "room",
  SYSTEM: "system",
} as const;

// Pagination defaults
export const ADMIN_PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// Storage constants
export const STORAGE_CONSTANTS = {
  TOTAL_CAPACITY: 200 * 1024 * 1024, // 200MB total capacity
  WARNING_THRESHOLD: 0.75, // 75%
  CRITICAL_THRESHOLD: 0.9, // 90%
  CLEANUP_THRESHOLD: 0.95, // 95%
} as const;

// File type categories for admin filtering
export const FILE_TYPE_CATEGORIES = {
  DOCUMENTS: ["pdf", "doc", "docx", "txt"],
  PRESENTATIONS: ["ppt", "pptx"],
  SPREADSHEETS: ["xls", "xlsx"],
  IMAGES: ["jpg", "jpeg", "png", "gif", "webp"],
  ALL: "all",
} as const;

// User role display names
export const USER_ROLE_DISPLAY = {
  student: "Student",
  lecturer: "Lecturer",
  admin: "Administrator",
} as const;

// Activity event types
export const ACTIVITY_EVENT_TYPES = {
  LOGIN: "login",
  LOGOUT: "logout",
  UPLOAD: "upload",
  SHARE: "share",
  JOIN_ROOM: "join_room",
  CREATE_ROOM: "create_room",
  DELETE_DOCUMENT: "delete_document",
  ADMIN_ACTION: "admin_action",
} as const;

// System health thresholds
export const SYSTEM_HEALTH = {
  EXCELLENT_THRESHOLD: 90,
  GOOD_THRESHOLD: 75,
  WARNING_THRESHOLD: 50,
  CRITICAL_THRESHOLD: 0,
} as const;

// Admin dashboard refresh intervals (in milliseconds)
export const REFRESH_INTERVALS = {
  SYSTEM_STATS: 30000, // 30 seconds
  ACTIVITY_FEED: 10000, // 10 seconds
  USER_COUNT: 60000, // 1 minute
  STORAGE_STATS: 60000, // 1 minute
} as const;

// Admin table column configurations
export const TABLE_COLUMNS = {
  USERS: [
    { key: "full_name", label: "Name", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "role", label: "Role", sortable: true },
    { key: "institution", label: "Institution", sortable: true },
    { key: "created_at", label: "Registered", sortable: true },
    { key: "last_activity", label: "Last Activity", sortable: true },
    { key: "document_count", label: "Documents", sortable: true },
    { key: "storage_used", label: "Storage Used", sortable: true },
  ],
  DOCUMENTS: [
    { key: "title", label: "Title", sortable: true },
    { key: "file_type", label: "Type", sortable: true },
    { key: "file_size", label: "Size", sortable: true },
    { key: "owner_name", label: "Owner", sortable: true },
    { key: "created_at", label: "Uploaded", sortable: true },
    { key: "is_shared", label: "Shared", sortable: true },
    { key: "share_count", label: "Share Count", sortable: true },
  ],
  ROOMS: [
    { key: "name", label: "Room Name", sortable: true },
    { key: "room_code", label: "Code", sortable: false },
    { key: "creator_name", label: "Creator", sortable: true },
    { key: "is_private", label: "Type", sortable: true },
    { key: "member_count", label: "Members", sortable: true },
    { key: "document_count", label: "Documents", sortable: true },
    { key: "created_at", label: "Created", sortable: true },
    { key: "activity_score", label: "Activity", sortable: true },
  ],
} as const;

// Export formats
export const EXPORT_FORMATS = {
  CSV: "csv",
  JSON: "json",
} as const;

// Admin notification types
export const ADMIN_NOTIFICATION_TYPES = {
  SYSTEM_ALERT: "system_alert",
  STORAGE_WARNING: "storage_warning",
  USER_REPORT: "user_report",
  SECURITY_ALERT: "security_alert",
  MAINTENANCE: "maintenance",
} as const;

// Date range presets for filtering
export const DATE_RANGE_PRESETS = {
  TODAY: "today",
  YESTERDAY: "yesterday",
  LAST_7_DAYS: "last_7_days",
  LAST_30_DAYS: "last_30_days",
  LAST_90_DAYS: "last_90_days",
  THIS_MONTH: "this_month",
  LAST_MONTH: "last_month",
  CUSTOM: "custom",
} as const;

// Chart colors for admin analytics
export const CHART_COLORS = {
  PRIMARY: "#3B82F6", // blue-500
  SUCCESS: "#10B981", // emerald-500
  WARNING: "#F59E0B", // amber-500
  DANGER: "#EF4444", // red-500
  INFO: "#6366F1", // indigo-500
  SECONDARY: "#6B7280", // gray-500
} as const;

// Admin menu items configuration
export const ADMIN_MENU_ITEMS = [
  {
    name: "Dashboard",
    path: ADMIN_ROUTES.DASHBOARD,
    icon: "dashboard",
    permissions: [ADMIN_PERMISSIONS.VIEW_ANALYTICS],
  },
  {
    name: "Users",
    path: ADMIN_ROUTES.USERS,
    icon: "users",
    permissions: [ADMIN_PERMISSIONS.VIEW_USERS],
  },
  {
    name: "Documents",
    path: ADMIN_ROUTES.DOCUMENTS,
    icon: "documents",
    permissions: [ADMIN_PERMISSIONS.VIEW_DOCUMENTS],
  },
  {
    name: "Rooms",
    path: ADMIN_ROUTES.ROOMS,
    icon: "rooms",
    permissions: [ADMIN_PERMISSIONS.VIEW_ROOMS],
  },
  {
    name: "Storage",
    path: ADMIN_ROUTES.STORAGE,
    icon: "storage",
    permissions: [ADMIN_PERMISSIONS.VIEW_ANALYTICS],
  },
  {
    name: "Activity",
    path: ADMIN_ROUTES.ACTIVITY,
    icon: "activity",
    permissions: [ADMIN_PERMISSIONS.VIEW_LOGS],
  },
  {
    name: "User Feedback",
    path: ADMIN_ROUTES.FEEDBACK,
    icon: "feedback",
    permissions: [ADMIN_PERMISSIONS.VIEW_FEEDBACK],
  },
  {
    name: "Settings",
    path: ADMIN_ROUTES.SETTINGS,
    icon: "settings",
    permissions: [ADMIN_PERMISSIONS.SYSTEM_SETTINGS],
  },
] as const;

// Error messages
export const ADMIN_ERROR_MESSAGES = {
  UNAUTHORIZED: "You do not have permission to access this resource",
  INVALID_ROLE: "Invalid user role specified",
  USER_NOT_FOUND: "User not found",
  DOCUMENT_NOT_FOUND: "Document not found",
  ROOM_NOT_FOUND: "Room not found",
  INVALID_ACTION: "Invalid admin action",
  SYSTEM_ERROR: "A system error occurred. Please try again.",
  NETWORK_ERROR: "Network error. Please check your connection.",
  VALIDATION_ERROR: "Please check your input and try again",
} as const;

// Success messages
export const ADMIN_SUCCESS_MESSAGES = {
  USER_UPDATED: "User updated successfully",
  USER_DEACTIVATED: "User deactivated successfully",
  USER_ACTIVATED: "User activated successfully",
  DOCUMENT_DELETED: "Document deleted successfully",
  ROOM_ARCHIVED: "Room archived successfully",
  ROOM_DELETED: "Room deleted successfully",
  SETTINGS_UPDATED: "Settings updated successfully",
  CLEANUP_COMPLETED: "Storage cleanup completed successfully",
  EXPORT_COMPLETED: "Data exported successfully",
} as const;
