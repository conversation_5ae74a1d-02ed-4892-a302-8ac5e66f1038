import { useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import { usePageTitle } from "../hooks/usePageTitle";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { FeedbackModal } from "../components/feedback/FeedbackModal";
import { feedbackService } from "../lib/feedbackService";
import { formatDateTime } from "../lib/utils";
import type { UserFeedback } from "../lib/feedbackService";

const categoryLabels = {
  bug_report: "Bug Report",
  feature_request: "Feature Request",
  general: "General Feedback",
  ui_ux: "UI/UX Issue",
};

const statusLabels = {
  pending: "Pending Review",
  reviewed: "Under Review",
  resolved: "Resolved",
};

const statusColors = {
  pending:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  reviewed: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  resolved:
    "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
};

export function FeedbackPage() {
  const { user } = useAuth();
  usePageTitle("Send Feedback");

  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [userFeedback, setUserFeedback] = useState<UserFeedback[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const loadUserFeedback = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const feedback = await feedbackService.getUserFeedback(user.id);
      setUserFeedback(feedback);
    } catch (error) {
      console.error("Failed to load user feedback:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFeedbackSubmitted = () => {
    loadUserFeedback();
  };

  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating
                ? "text-yellow-400"
                : "text-gray-300 dark:text-gray-600"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
          {rating}/5
        </span>
      </div>
    );
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                <span className="text-white text-2xl">💬</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  Send Feedback
                </h1>
                <p className="mt-1 text-gray-600 dark:text-gray-400">
                  Help us improve BrimBag by sharing your thoughts and
                  suggestions
                </p>
              </div>
            </div>
          </div>

          {/* Main Feedback Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Submit Feedback Card */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">✨</span>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Share Your Experience
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Your feedback helps us build a better platform for everyone.
                  Tell us about bugs, suggest features, or share general
                  thoughts.
                </p>
                <button
                  onClick={() => setShowFeedbackModal(true)}
                  className="w-full px-6 py-3 text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] shadow-lg shadow-blue-500/25"
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>💬</span>
                    <span>Write Feedback</span>
                  </span>
                </button>
              </div>
            </div>

            {/* Feedback Categories */}
            <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
                What can you share?
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-red-600 dark:text-red-400 text-sm">
                      🐛
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Bug Reports
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Found something broken? Let us know!
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-purple-600 dark:text-purple-400 text-sm">
                      💡
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Feature Requests
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Have an idea for a new feature?
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-indigo-600 dark:text-indigo-400 text-sm">
                      🎨
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      UI/UX Feedback
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Thoughts on design and usability?
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-gray-600 dark:text-gray-400 text-sm">
                      💭
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      General Feedback
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Any other thoughts or suggestions?
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Your Previous Feedback */}
          <div className="mt-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Your Previous Feedback
              </h2>
              <button
                onClick={loadUserFeedback}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors disabled:opacity-50"
              >
                {isLoading ? "Loading..." : "Refresh"}
              </button>
            </div>

            {userFeedback.length === 0 ? (
              <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📝</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No feedback yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Your submitted feedback will appear here. Start by sharing
                  your first feedback!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {userFeedback.map((feedback) => (
                  <div
                    key={feedback.id}
                    className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            statusColors[feedback.status]
                          }`}
                        >
                          {statusLabels[feedback.status]}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {categoryLabels[feedback.category]}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        {renderStarRating(feedback.rating)}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDateTime(feedback.created_at)}
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      {feedback.feedback_text}
                    </p>
                    {feedback.admin_notes && (
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                        <p className="text-xs font-medium text-blue-800 dark:text-blue-300 mb-1">
                          Admin Response:
                        </p>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          {feedback.admin_notes}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && user && (
        <FeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          userId={user.id}
          onFeedbackSubmitted={handleFeedbackSubmitted}
        />
      )}
    </DashboardLayout>
  );
}
