import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";

export function RoomManagement() {
  usePageTitle("Room Management - Admin");

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
            Room Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
            This comprehensive room oversight interface will be implemented in
            Stage 7 of the development plan.
          </p>
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 transition-theme duration-theme">
            <p className="text-sm text-purple-700 dark:text-purple-300 transition-theme duration-theme">
              🚀 Coming soon: Room moderation, member management, analytics, and
              more!
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
