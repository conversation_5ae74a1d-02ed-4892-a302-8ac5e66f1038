import { DashboardLayout } from "../components/layout/DashboardLayout";
import { InvitationsList } from "../components/invitations/InvitationsList";
import { usePageTitle } from "../hooks/usePageTitle";

export function Invitations() {
  // Set page title
  usePageTitle("Room Invitations");

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Room Invitations</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage your room invitations and join new rooms
            </p>
          </div>

          {/* Invitations List */}
          <InvitationsList />
        </div>
      </div>
    </DashboardLayout>
  );
}
