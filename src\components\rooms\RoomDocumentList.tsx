import { useState } from "react";
import type { RoomDocument } from "../../lib/roomService";
import { formatDateTime, formatFileSize } from "../../lib/utils";
import { fileService } from "../../lib/fileService";
import toast from "react-hot-toast";
import { DocumentPreview } from "../files/DocumentPreview";
import type { DocumentFile } from "../../lib/fileService";

interface RoomDocumentListProps {
  documents: RoomDocument[];
  isLoading: boolean;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onRemoveDocument: (roomDocumentId: string) => void;
  canManageDocuments: boolean;
  currentUserId: string;
}

interface FileTypeFilter {
  label: string;
  value: string;
  icon: string;
}

const FILE_TYPE_FILTERS: FileTypeFilter[] = [
  { label: "All Files", value: "", icon: "📄" },
  { label: "PDFs", value: "pdf", icon: "📕" },
  { label: "Documents", value: "doc", icon: "📝" },
  { label: "Presentations", value: "ppt", icon: "📊" },
  { label: "Spreadsheets", value: "xls", icon: "📈" },
  { label: "Images", value: "image", icon: "🖼️" },
  { label: "Text Files", value: "txt", icon: "📃" },
];

export function RoomDocumentList({
  documents,
  isLoading,
  searchQuery,
  onSearchChange,
  onRemoveDocument,
  canManageDocuments,
  currentUserId,
}: RoomDocumentListProps) {
  const [selectedFileType, setSelectedFileType] = useState("");
  const [sortBy, setSortBy] = useState<"date" | "name" | "size">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [previewDocument, setPreviewDocument] = useState<DocumentFile | null>(
    null
  );

  const handlePreview = (roomDocument: RoomDocument) => {
    if (!roomDocument.documents) return;
    setPreviewDocument(roomDocument.documents);
  };

  const handleDownload = async (roomDocument: RoomDocument) => {
    if (!roomDocument.documents) return;

    try {
      const downloadUrl = await fileService.getDownloadUrl(
        roomDocument.documents.file_path
      );

      // Create a temporary link and click it to trigger download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = roomDocument.documents.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Update download count
      await fileService.incrementDownloadCount(roomDocument.documents.id);
      toast.success("Download started");
    } catch (error: any) {
      toast.error(error.message || "Failed to download file");
    }
  };

  const filteredAndSortedDocuments = documents
    .filter((doc) => {
      // Text search filter
      const matchesSearch =
        doc.documents?.title
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        doc.documents?.description
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        doc.shared_by_profile?.full_name
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase());

      // File type filter
      const matchesFileType =
        !selectedFileType ||
        doc.documents?.file_type
          .toLowerCase()
          .includes(selectedFileType.toLowerCase()) ||
        (selectedFileType === "image" &&
          doc.documents?.mime_type.startsWith("image/"));

      return matchesSearch && matchesFileType;
    })
    .sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "name":
          comparison = (a.documents?.title || "").localeCompare(
            b.documents?.title || ""
          );
          break;
        case "size":
          comparison =
            (a.documents?.file_size || 0) - (b.documents?.file_size || 0);
          break;
        case "date":
        default:
          comparison =
            new Date(a.shared_at).getTime() - new Date(b.shared_at).getTime();
          break;
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

  const canRemoveDocument = (doc: RoomDocument) => {
    return (
      canManageDocuments ||
      doc.shared_by === currentUserId ||
      doc.documents?.user_id === currentUserId
    );
  };

  const getFileIcon = (fileType: string, mimeType: string) => {
    if (mimeType.startsWith("image/")) return "🖼️";
    if (fileType.toLowerCase().includes("pdf")) return "📕";
    if (fileType.toLowerCase().includes("doc")) return "📝";
    if (fileType.toLowerCase().includes("ppt")) return "📊";
    if (fileType.toLowerCase().includes("xls")) return "📈";
    if (fileType.toLowerCase().includes("txt")) return "📃";
    return "📄";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-gray-400 dark:text-gray-500 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search documents, descriptions, or shared by..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md leading-5 bg-white dark:bg-dark-800 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-500 focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-theme duration-theme"
          />
        </div>

        {/* Filters and Sort */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          {/* File Type Filter */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-theme duration-theme">
              Filter:
            </span>
            <div className="flex flex-wrap gap-2">
              {FILE_TYPE_FILTERS.map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setSelectedFileType(filter.value)}
                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                    selectedFileType === filter.value
                      ? "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-300 dark:border-blue-700"
                      : "bg-white dark:bg-dark-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-dark-600 hover:bg-gray-50 dark:hover:bg-dark-700"
                  }`}
                >
                  <span className="mr-1">{filter.icon}</span>
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) =>
                setSortBy(e.target.value as "date" | "name" | "size")
              }
              className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="date">Date Shared</option>
              <option value="name">Name</option>
              <option value="size">File Size</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              className="p-1 text-gray-600 hover:text-gray-800"
              title={`Sort ${sortOrder === "asc" ? "descending" : "ascending"}`}
            >
              <svg
                className={`w-4 h-4 transform ${
                  sortOrder === "desc" ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 11l5-5m0 0l5 5m-5-5v12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Documents List */}
      {filteredAndSortedDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
            {searchQuery || selectedFileType
              ? "No documents found"
              : "No documents shared yet"}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            {searchQuery || selectedFileType
              ? "Try adjusting your search terms or filters"
              : "Start sharing documents to this room to see them here."}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredAndSortedDocuments.map((roomDoc) => (
            <div
              key={roomDoc.id}
              className="card p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* File Icon */}
                  <div className="text-3xl">
                    {getFileIcon(
                      roomDoc.documents?.file_type || "",
                      roomDoc.documents?.mime_type || ""
                    )}
                  </div>

                  {/* Document Info */}
                  <div className="flex-1 min-w-0">
                    <h3
                      className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 truncate cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-theme duration-theme"
                      onClick={() => handlePreview(roomDoc)}
                      title="Click to preview"
                    >
                      {roomDoc.documents?.title}
                    </h3>
                    {roomDoc.documents?.description && (
                      <p className="text-gray-600 mb-2 line-clamp-2">
                        {roomDoc.documents.description}
                      </p>
                    )}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                        {formatFileSize(roomDoc.documents?.file_size || 0)}
                      </span>
                      <span className="flex items-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        {roomDoc.shared_by_profile?.full_name ||
                          roomDoc.shared_by_profile?.email}
                      </span>
                      <span className="flex items-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {formatDateTime(roomDoc.shared_at)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      roomDoc.permission === "download"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                    }`}
                  >
                    {roomDoc.permission}
                  </span>

                  {/* Preview Button - Always available */}
                  <button
                    onClick={() => handlePreview(roomDoc)}
                    className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20 rounded-md transition-colors"
                    title="Preview document"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  </button>

                  {roomDoc.permission === "download" && (
                    <button
                      onClick={() => handleDownload(roomDoc)}
                      className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20 rounded-md transition-colors"
                      title="Download document"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </button>
                  )}

                  {canRemoveDocument(roomDoc) && (
                    <button
                      onClick={() => onRemoveDocument(roomDoc.id)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                      title="Remove from room"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Results Summary */}
      {filteredAndSortedDocuments.length > 0 && (
        <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400 transition-theme duration-theme">
          Showing {filteredAndSortedDocuments.length} of {documents.length}{" "}
          documents
        </div>
      )}

      {/* Document Preview Modal */}
      {previewDocument && (
        <DocumentPreview
          document={previewDocument}
          onClose={() => setPreviewDocument(null)}
          onDownload={() => {
            // Update download count and close preview
            setPreviewDocument(null);
          }}
        />
      )}
    </div>
  );
}
