import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAuth } from "../../contexts/AuthContext";
import { cn, validatePassword } from "../../lib/utils";
import { usePageTitle } from "../../hooks/usePageTitle";
import { LoadingButton } from "../ui/LoadingSpinner";
import { BrimBagLogo } from "../ui/BrimBagLogo";

const _registerSchema = z
  .object({
    fullName: z.string().min(2, "Full name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string(),
    terms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type RegisterFormData = z.infer<typeof _registerSchema>;

export function RegisterForm() {
  const { signUp, actionLoading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [emailAlreadyExists, setEmailAlreadyExists] = useState(false);

  // Set page title
  usePageTitle("Sign Up");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    watch,
  } = useForm<RegisterFormData>();

  const password = watch("password");
  const passwordValidation = password
    ? validatePassword(password)
    : { isValid: true, errors: [] };

  const onSubmit = async (data: RegisterFormData) => {
    try {
      // Reset email already exists state
      setEmailAlreadyExists(false);
      await signUp({
        email: data.email,
        password: data.password,
        fullName: data.fullName,
      });
    } catch (error: any) {
      const errorMessage =
        error?.message || "An error occurred during registration";

      if (errorMessage.includes("already exists")) {
        // Email already exists - show error on email field and set flag
        setError("email", { message: errorMessage });
        setEmailAlreadyExists(true);
      } else if (errorMessage.includes("already registered")) {
        // Fallback for other "already registered" messages
        setError("email", {
          message: "This email is already registered. Please sign in instead.",
        });
        setEmailAlreadyExists(true);
      } else {
        // Other errors - show on email field
        setError("email", { message: errorMessage });
        setEmailAlreadyExists(false);
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-theme duration-theme">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 rounded-full flex items-center justify-center transition-theme duration-theme">
            <BrimBagLogo size="xl" variant="white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
            Join BrimBag
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Create your account to start organizing your documents
          </p>
        </div>

        <div className="card p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label
                htmlFor="fullName"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Full Name
              </label>
              <input
                {...register("fullName")}
                type="text"
                autoComplete="name"
                disabled={actionLoading}
                className={cn(
                  "input-field",
                  errors.fullName && "border-red-300 focus:ring-red-500",
                  actionLoading && "opacity-60 cursor-not-allowed"
                )}
                placeholder="Enter your full name"
              />
              {errors.fullName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.fullName.message}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Email address
              </label>
              <input
                {...register("email")}
                type="email"
                autoComplete="email"
                disabled={actionLoading}
                className={cn(
                  "input-field",
                  errors.email && "border-red-300 focus:ring-red-500",
                  actionLoading && "opacity-60 cursor-not-allowed"
                )}
                placeholder="Enter your email"
                onChange={(e) => {
                  // Reset error states when user starts typing
                  if (emailAlreadyExists) {
                    setEmailAlreadyExists(false);
                  }
                  // Call the original onChange from react-hook-form
                  register("email").onChange(e);
                }}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.email.message}
                </p>
              )}
              {emailAlreadyExists && (
                <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg transition-theme duration-theme">
                  <p className="text-sm text-blue-700 dark:text-blue-300 transition-theme duration-theme">
                    Already have an account?{" "}
                    <Link
                      to="/login"
                      className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-200"
                    >
                      Sign in here
                    </Link>
                  </p>
                </div>
              )}
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Password
              </label>
              <div className="relative">
                <input
                  {...register("password")}
                  type={showPassword ? "text" : "password"}
                  autoComplete="new-password"
                  disabled={actionLoading}
                  className={cn(
                    "input-field pr-10",
                    errors.password && "border-red-300 focus:ring-red-500",
                    actionLoading && "opacity-60 cursor-not-allowed"
                  )}
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.password.message}
                </p>
              )}
              {password && !passwordValidation.isValid && (
                <div className="mt-2 space-y-1">
                  {passwordValidation.errors.map((error, index) => (
                    <p
                      key={index}
                      className="text-xs text-red-600 dark:text-red-400 transition-theme duration-theme"
                    >
                      • {error}
                    </p>
                  ))}
                </div>
              )}
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme"
              >
                Confirm Password
              </label>
              <div className="relative">
                <input
                  {...register("confirmPassword")}
                  type={showConfirmPassword ? "text" : "password"}
                  autoComplete="new-password"
                  disabled={actionLoading}
                  className={cn(
                    "input-field pr-10",
                    errors.confirmPassword &&
                      "border-red-300 focus:ring-red-500",
                    actionLoading && "opacity-60 cursor-not-allowed"
                  )}
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            <div className="flex items-center">
              <input
                {...register("terms")}
                id="terms"
                type="checkbox"
                disabled={actionLoading}
                className={cn(
                  "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",
                  actionLoading && "opacity-60 cursor-not-allowed"
                )}
              />
              <label
                htmlFor="terms"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300 transition-theme duration-theme"
              >
                I agree to the{" "}
                <Link
                  to="/terms"
                  className="text-primary-600 hover:text-primary-500 font-medium"
                >
                  Terms and Conditions
                </Link>{" "}
                and{" "}
                <Link
                  to="/privacy"
                  className="text-primary-600 hover:text-primary-500 font-medium"
                >
                  Privacy Policy
                </Link>
              </label>
            </div>
            {errors.terms && (
              <p className="text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
                {errors.terms.message}
              </p>
            )}

            <LoadingButton
              type="submit"
              loading={actionLoading}
              loadingText="Creating account..."
              variant="primary"
              className="w-full"
            >
              Create account
            </LoadingButton>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  Already have an account?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                to="/login"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-dark-600 rounded-lg shadow-sm bg-white dark:bg-dark-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-primary-400 dark:focus:ring-offset-dark-800 transition-theme duration-theme"
              >
                Sign in to your account
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
