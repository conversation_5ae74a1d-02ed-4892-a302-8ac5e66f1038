# Admin Setup Guide - User Deletion Fix

## Issue

Users were not being completely deleted from the system. The deletion process was only removing user data from application tables but not from the authentication system.

## Root Cause

The `admin_delete_user` database function was only deleting from the `profiles` table and other application tables, but not from Supabase's `auth.users` table. This left authentication records orphaned.

## Solution

1. **Database Function**: Updated to handle application data cleanup only
2. **Client-side Admin API**: Added proper authentication record deletion using Supabase's admin API
3. **Service Role Key**: Required for admin operations

## Setup Steps

### 1. Update Environment Variables

Add the service role key to your `.env` file:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

**Important**: The service role key can be found in your Supabase project settings under API. This key has elevated privileges and should be kept secure.

### 2. Update Database Functions

Run **either** of these SQL files in your Supabase SQL Editor:

- `database/complete-user-deletion-fix.sql` (recommended - clean, focused fix)
- OR `database/user-management-functions.sql` (full file with all functions)

Both contain the same fixes for the column name errors.

### 3. Test User Deletion

1. Go to Admin Panel → User Management
2. Try deleting a test user
3. Verify that:
   - User data is removed from all application tables
   - User profile is deleted
   - Authentication record is removed (user cannot log in)
   - User disappears from both admin panel and Supabase auth users

## What's Fixed

- ✅ Complete user deletion (both application data and auth records)
- ✅ Proper error handling and logging
- ✅ Storage cleanup (user files are removed)
- ✅ Foreign key constraint handling
- ✅ Admin action logging

## Security Notes

- Service role key bypasses Row Level Security (RLS)
- Only use for legitimate admin operations
- Keep the service role key secure and never expose it in client-side code
- The admin client is only created if the service role key is available

## Fallback Behavior

If the service role key is not provided:

- Application data will still be cleaned up
- A warning will be logged about the missing auth record deletion
- The operation will still be considered successful for application purposes

## Testing

1. Create a test user account
2. Upload some documents as that user
3. Join some rooms as that user
4. Delete the user from admin panel
5. Verify complete removal from system
6. Try logging in with deleted user credentials (should fail)
