-- SmartBagPack Database Schema
-- Run this SQL in your Supabase SQL Editor

-- Create profiles table (if not exists)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT CHECK (role IN ('student', 'lecturer', 'admin')) DEFAULT 'student',
  institution TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rooms table
CREATE TABLE IF NOT EXISTS rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  room_code TEXT UNIQUE NOT NULL,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  is_private BOOLEAN DEFAULT false,
  max_members INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_members table
CREATE TABLE IF NOT EXISTS room_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT CHECK (role IN ('admin', 'member')) DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, user_id)
);

-- Create folders table
CREATE TABLE IF NOT EXISTS folders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  path TEXT NOT NULL, -- Materialized path for efficient queries (e.g., '/folder1/subfolder2/')
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, parent_folder_id, name), -- Prevent duplicate folder names in same parent
  CHECK (parent_folder_id != id) -- Prevent self-reference
);

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  file_path TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  file_type TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  is_public BOOLEAN DEFAULT false,
  download_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_documents table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS room_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  shared_by UUID REFERENCES auth.users(id) NOT NULL,
  permission TEXT CHECK (permission IN ('view', 'download')) DEFAULT 'download',
  shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, document_id)
);

-- Create document_shares table (for direct sharing)
CREATE TABLE IF NOT EXISTS document_shares (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  original_document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  shared_document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  shared_by UUID REFERENCES auth.users(id) NOT NULL,
  shared_with UUID REFERENCES auth.users(id) NOT NULL,
  permission TEXT CHECK (permission IN ('view', 'download')) DEFAULT 'download',
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(original_document_id, shared_with)
);

-- Create room_invitations table
CREATE TABLE IF NOT EXISTS room_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  invited_by UUID REFERENCES auth.users(id) NOT NULL,
  invited_user UUID REFERENCES auth.users(id) NOT NULL,
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined', 'expired')) DEFAULT 'pending',
  message TEXT,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, invited_user)
);

-- Create room_invitation_links table (for shareable links)
CREATE TABLE IF NOT EXISTS room_invitation_links (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  link_token TEXT UNIQUE NOT NULL,
  max_uses INTEGER DEFAULT NULL, -- NULL means unlimited
  current_uses INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'room_invitation', 'room_join', 'room_leave', 'document_shared_to_room',
    'document_shared_direct', 'room_document_uploaded', 'storage_warning',
    'system_announcement', 'room_settings_changed'
  )),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}', -- Additional data like room_id, document_id, etc.
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_feedback table
CREATE TABLE IF NOT EXISTS user_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  feedback_text TEXT NOT NULL CHECK (length(feedback_text) <= 1000),
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  category TEXT NOT NULL CHECK (category IN ('bug_report', 'feature_request', 'general', 'ui_ux')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved')),
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent_folder_id ON folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path);
CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
CREATE INDEX IF NOT EXISTS idx_documents_folder_id ON documents(folder_id);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_file_type ON documents(file_type);
CREATE INDEX IF NOT EXISTS idx_room_documents_room_id ON room_documents(room_id);
CREATE INDEX IF NOT EXISTS idx_room_documents_document_id ON room_documents(document_id);
CREATE INDEX IF NOT EXISTS idx_room_members_room_id ON room_members(room_id);
CREATE INDEX IF NOT EXISTS idx_room_members_user_id ON room_members(user_id);
CREATE INDEX IF NOT EXISTS idx_rooms_room_code ON rooms(room_code);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_status ON user_feedback(status);
CREATE INDEX IF NOT EXISTS idx_user_feedback_category ON user_feedback(category);
CREATE INDEX IF NOT EXISTS idx_user_feedback_created_at ON user_feedback(created_at DESC);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_invitation_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for folders
CREATE POLICY "Users can view own folders" ON folders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create folders" ON folders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own folders" ON folders FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own folders" ON folders FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for rooms
CREATE POLICY "Users can view rooms they are members of" ON rooms FOR SELECT USING (
  id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid())
  OR created_by = auth.uid()
  OR is_private = false
);
CREATE POLICY "Users can create rooms" ON rooms FOR INSERT WITH CHECK (auth.uid() = created_by);
CREATE POLICY "Room creators can update their rooms" ON rooms FOR UPDATE USING (auth.uid() = created_by);
CREATE POLICY "Room creators can delete their rooms" ON rooms FOR DELETE USING (auth.uid() = created_by);

-- RLS Policies for room_members
CREATE POLICY "Users can view room members of rooms they belong to" ON room_members FOR SELECT USING (
  room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid())
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Room admins can manage members" ON room_members FOR ALL USING (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR (room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin'))
);

-- RLS Policies for documents
CREATE POLICY "Users can view own documents" ON documents FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view public documents" ON documents FOR SELECT USING (is_public = true);
CREATE POLICY "Users can view shared documents" ON documents FOR SELECT USING (
  id IN (SELECT shared_document_id FROM document_shares WHERE shared_with = auth.uid())
  OR id IN (
    SELECT rd.document_id FROM room_documents rd
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);
CREATE POLICY "Users can create documents" ON documents FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own documents" ON documents FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own documents" ON documents FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for room_documents
CREATE POLICY "Users can view room documents of their rooms" ON room_documents FOR SELECT USING (
  room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid())
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Room members can share documents to rooms" ON room_documents FOR INSERT WITH CHECK (
  room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid())
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Document owners and room admins can manage room documents" ON room_documents FOR DELETE USING (
  shared_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
);

-- RLS Policies for document_shares
CREATE POLICY "Users can view shares involving them" ON document_shares FOR SELECT USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);
CREATE POLICY "Document owners can create shares" ON document_shares FOR INSERT WITH CHECK (
  document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
);
CREATE POLICY "Share creators can delete shares" ON document_shares FOR DELETE USING (shared_by = auth.uid());

-- Create functions for automatic room membership
CREATE OR REPLACE FUNCTION add_room_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO room_members (room_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'admin');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update folder path automatically
CREATE OR REPLACE FUNCTION update_folder_path()
RETURNS TRIGGER AS $$
DECLARE
  parent_path TEXT;
BEGIN
  -- If this is a root folder (no parent), set path to just the folder name
  IF NEW.parent_folder_id IS NULL THEN
    NEW.path := '/' || NEW.name || '/';
  ELSE
    -- Get parent folder path
    SELECT path INTO parent_path FROM folders WHERE id = NEW.parent_folder_id;
    IF parent_path IS NULL THEN
      RAISE EXCEPTION 'Parent folder not found';
    END IF;
    -- Construct new path
    NEW.path := parent_path || NEW.name || '/';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically add room creator as admin
CREATE TRIGGER trigger_add_room_creator_as_admin
  AFTER INSERT ON rooms
  FOR EACH ROW
  EXECUTE FUNCTION add_room_creator_as_admin();

-- Create trigger to automatically update folder paths
CREATE TRIGGER trigger_update_folder_path
  BEFORE INSERT OR UPDATE ON folders
  FOR EACH ROW
  EXECUTE FUNCTION update_folder_path();

-- RLS Policies for room_invitations
CREATE POLICY "Users can view invitations involving them" ON room_invitations FOR SELECT USING (
  invited_by = auth.uid() OR invited_user = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Room admins can create invitations" ON room_invitations FOR INSERT WITH CHECK (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Invited users can update invitation status" ON room_invitations FOR UPDATE USING (
  invited_user = auth.uid()
);
CREATE POLICY "Invitation creators can delete invitations" ON room_invitations FOR DELETE USING (
  invited_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);

-- RLS Policies for room_invitation_links
CREATE POLICY "Room admins can view invitation links" ON room_invitation_links FOR SELECT USING (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Room admins can create invitation links" ON room_invitation_links FOR INSERT WITH CHECK (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Link creators can update invitation links" ON room_invitation_links FOR UPDATE USING (
  created_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Link creators can delete invitation links" ON room_invitation_links FOR DELETE USING (
  created_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (
  user_id = auth.uid()
);
CREATE POLICY "System can create notifications" ON notifications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (
  user_id = auth.uid()
);
CREATE POLICY "Users can delete own notifications" ON notifications FOR DELETE USING (
  user_id = auth.uid()
);

-- Create function to generate unique room codes
CREATE OR REPLACE FUNCTION generate_room_code()
RETURNS TEXT AS $$
DECLARE
  chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  result TEXT := '';
  i INTEGER;
BEGIN
  FOR i IN 1..6 LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies for user_feedback
CREATE POLICY "Users can view own feedback" ON user_feedback FOR SELECT USING (
  user_id IN (SELECT id FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can create feedback" ON user_feedback FOR INSERT WITH CHECK (
  user_id IN (SELECT id FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Admins can view all feedback" ON user_feedback FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Admins can update feedback" ON user_feedback FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Admins can delete feedback" ON user_feedback FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Create function to generate unique invitation link tokens
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS TEXT AS $$
DECLARE
  chars TEXT := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  result TEXT := '';
  i INTEGER;
BEGIN
  FOR i IN 1..32 LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;
