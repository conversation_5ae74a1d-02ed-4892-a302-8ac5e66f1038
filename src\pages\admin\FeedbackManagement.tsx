import { useState, useEffect } from "react";
import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";
import { feedbackService } from "../../lib/feedbackService";
import { formatDateTime } from "../../lib/utils";
import { AlertSystem, useAlerts } from "../../components/admin/AlertSystem";
import { FeedbackDetailsModal } from "../../components/admin/FeedbackDetailsModal";
import { FeedbackStatsDashboard } from "../../components/admin/FeedbackStatsDashboard";
import type {
  FeedbackWithProfile,
  FeedbackFilters,
  FeedbackStats,
  PaginatedFeedbackResponse,
  FeedbackCategory,
  FeedbackStatus,
} from "../../lib/feedbackService";

const categoryLabels: Record<FeedbackCategory, string> = {
  bug_report: "Bug Report",
  feature_request: "Feature Request",
  general: "General Feedback",
  ui_ux: "UI/UX Issue",
};

const statusLabels: Record<FeedbackStatus, string> = {
  pending: "Pending",
  reviewed: "Reviewed",
  resolved: "Resolved",
};

const statusColors: Record<FeedbackStatus, string> = {
  pending:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  reviewed: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  resolved:
    "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
};

const categoryColors: Record<FeedbackCategory, string> = {
  bug_report: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
  feature_request:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
  general: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
  ui_ux:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
};

export function FeedbackManagement() {
  usePageTitle("User Feedback - Admin");

  const [feedback, setFeedback] = useState<PaginatedFeedbackResponse>({
    data: [],
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
  });
  const [, setStats] = useState<FeedbackStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FeedbackFilters>({});
  const [selectedFeedback, setSelectedFeedback] =
    useState<FeedbackWithProfile | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { alerts, showSuccess, showError, removeAlert } = useAlerts();

  useEffect(() => {
    loadFeedback();
    loadStats();
  }, [filters, feedback.page]);

  const loadFeedback = async () => {
    try {
      setIsLoading(true);
      const response = await feedbackService.getAllFeedback(
        feedback.page,
        feedback.limit,
        filters
      );
      setFeedback(response);
    } catch (error: any) {
      console.error("Failed to load feedback:", error);
      showError("Failed to load feedback", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await feedbackService.getFeedbackStats();
      setStats(statsData);
    } catch (error: any) {
      console.error("Failed to load feedback stats:", error);
    }
  };

  const handleFilterChange = (newFilters: Partial<FeedbackFilters>) => {
    setFilters({ ...filters, ...newFilters });
    setFeedback({ ...feedback, page: 1 });
  };

  const handleStatusUpdate = async (
    feedbackId: string,
    status: FeedbackStatus
  ) => {
    try {
      await feedbackService.updateFeedback(feedbackId, { status });
      showSuccess("Status updated", "Feedback status updated successfully");
      loadFeedback();
      loadStats();
    } catch (error: any) {
      console.error("Failed to update status:", error);
      showError("Update failed", error.message);
    }
  };

  const handleDelete = async (feedbackId: string) => {
    if (!confirm("Are you sure you want to delete this feedback?")) return;

    try {
      await feedbackService.deleteFeedback(feedbackId);
      showSuccess("Feedback deleted", "Feedback deleted successfully");
      loadFeedback();
      loadStats();
    } catch (error: any) {
      console.error("Failed to delete feedback:", error);
      showError("Delete failed", error.message);
    }
  };

  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating
                ? "text-yellow-400"
                : "text-gray-300 dark:text-gray-600"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
          {rating}/5
        </span>
      </div>
    );
  };

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AlertSystem alerts={alerts} onRemoveAlert={removeAlert} />

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            User Feedback Management
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Monitor and manage user feedback to improve BrimBag
          </p>
        </div>

        {/* Statistics Dashboard */}
        <div className="mb-8">
          <FeedbackStatsDashboard onStatsLoad={setStats} />
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Filters
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  handleFilterChange({
                    status: e.target.value as FeedbackStatus | undefined,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100"
              >
                <option value="">All Statuses</option>
                {Object.entries(statusLabels).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                value={filters.category || ""}
                onChange={(e) =>
                  handleFilterChange({
                    category: e.target.value as FeedbackCategory | undefined,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100"
              >
                <option value="">All Categories</option>
                {Object.entries(categoryLabels).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Rating
              </label>
              <select
                value={filters.rating || ""}
                onChange={(e) =>
                  handleFilterChange({
                    rating: e.target.value
                      ? parseInt(e.target.value)
                      : undefined,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100"
              >
                <option value="">All Ratings</option>
                <option value="5">5 Stars</option>
                <option value="4">4 Stars</option>
                <option value="3">3 Stars</option>
                <option value="2">2 Stars</option>
                <option value="1">1 Star</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search
              </label>
              <input
                type="text"
                placeholder="Search feedback..."
                value={filters.search || ""}
                onChange={(e) =>
                  handleFilterChange({ search: e.target.value || undefined })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Feedback List */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-dark-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Feedback ({feedback.total})
            </h3>
          </div>

          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Loading feedback...
              </p>
            </div>
          ) : feedback.data.length === 0 ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No feedback found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                No feedback matches your current filters.
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-dark-700">
              {feedback.data.map((item) => (
                <div
                  key={item.id}
                  className="p-6 hover:bg-gray-50 dark:hover:bg-dark-700/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              {item.profiles?.full_name?.charAt(0) ||
                                item.profiles?.email?.charAt(0).toUpperCase() ||
                                "U"}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {item.profiles?.full_name || "Anonymous User"}
                            </h4>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {item.profiles?.email}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            {renderStarRating(item.rating)}
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDateTime(item.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mb-3">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            statusColors[item.status]
                          }`}
                        >
                          {statusLabels[item.status]}
                        </span>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            categoryColors[item.category]
                          }`}
                        >
                          {categoryLabels[item.category]}
                        </span>
                      </div>

                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-4 line-clamp-3">
                        {item.feedback_text}
                      </p>

                      {item.admin_notes && (
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
                          <p className="text-xs font-medium text-blue-800 dark:text-blue-300 mb-1">
                            Admin Notes:
                          </p>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {item.admin_notes}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {item.status === "pending" && (
                        <>
                          <button
                            onClick={() =>
                              handleStatusUpdate(item.id, "reviewed")
                            }
                            className="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50 rounded-md transition-colors"
                          >
                            Mark Reviewed
                          </button>
                          <button
                            onClick={() =>
                              handleStatusUpdate(item.id, "resolved")
                            }
                            className="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50 rounded-md transition-colors"
                          >
                            Mark Resolved
                          </button>
                        </>
                      )}
                      {item.status === "reviewed" && (
                        <button
                          onClick={() =>
                            handleStatusUpdate(item.id, "resolved")
                          }
                          className="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50 rounded-md transition-colors"
                        >
                          Mark Resolved
                        </button>
                      )}
                      <button
                        onClick={() => {
                          setSelectedFeedback(item);
                          setShowDetailsModal(true);
                        }}
                        className="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-dark-600 dark:text-gray-300 dark:hover:bg-dark-500 rounded-md transition-colors"
                      >
                        Details
                      </button>
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50 rounded-md transition-colors"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {feedback.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-dark-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Showing {(feedback.page - 1) * feedback.limit + 1} to{" "}
                  {Math.min(feedback.page * feedback.limit, feedback.total)} of{" "}
                  {feedback.total} results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      setFeedback({ ...feedback, page: feedback.page - 1 })
                    }
                    disabled={feedback.page === 1}
                    className="px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-dark-700 dark:border-dark-600 dark:text-gray-300 dark:hover:bg-dark-600"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Page {feedback.page} of {feedback.totalPages}
                  </span>
                  <button
                    onClick={() =>
                      setFeedback({ ...feedback, page: feedback.page + 1 })
                    }
                    disabled={feedback.page === feedback.totalPages}
                    className="px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-dark-700 dark:border-dark-600 dark:text-gray-300 dark:hover:bg-dark-600"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Feedback Details Modal */}
        {showDetailsModal && selectedFeedback && (
          <FeedbackDetailsModal
            feedback={selectedFeedback}
            isOpen={showDetailsModal}
            onClose={() => {
              setShowDetailsModal(false);
              setSelectedFeedback(null);
            }}
            onUpdate={() => {
              loadFeedback();
              loadStats();
            }}
          />
        )}
      </div>
    </AdminLayout>
  );
}
