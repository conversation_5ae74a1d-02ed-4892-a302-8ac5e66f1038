import { Toaster } from "react-hot-toast";
import { useTheme } from "../../contexts/ThemeContext";

export function ThemeAwareToaster() {
  const { theme } = useTheme();

  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 4000,
        style: {
          background: theme === 'dark' ? '#1e293b' : '#ffffff',
          color: theme === 'dark' ? '#f1f5f9' : '#1f2937',
          border: theme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
          borderRadius: '0.75rem',
          boxShadow: theme === 'dark' 
            ? '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)'
            : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        },
        success: {
          duration: 3000,
          iconTheme: {
            primary: theme === 'dark' ? '#34d399' : '#10b981',
            secondary: theme === 'dark' ? '#1e293b' : '#ffffff',
          },
          style: {
            background: theme === 'dark' ? '#1e293b' : '#ffffff',
            color: theme === 'dark' ? '#f1f5f9' : '#1f2937',
            border: theme === 'dark' ? '1px solid #059669' : '1px solid #10b981',
          },
        },
        error: {
          duration: 4000,
          iconTheme: {
            primary: theme === 'dark' ? '#f87171' : '#ef4444',
            secondary: theme === 'dark' ? '#1e293b' : '#ffffff',
          },
          style: {
            background: theme === 'dark' ? '#1e293b' : '#ffffff',
            color: theme === 'dark' ? '#f1f5f9' : '#1f2937',
            border: theme === 'dark' ? '1px solid #dc2626' : '1px solid #ef4444',
          },
        },
        loading: {
          iconTheme: {
            primary: theme === 'dark' ? '#60a5fa' : '#3b82f6',
            secondary: theme === 'dark' ? '#1e293b' : '#ffffff',
          },
          style: {
            background: theme === 'dark' ? '#1e293b' : '#ffffff',
            color: theme === 'dark' ? '#f1f5f9' : '#1f2937',
            border: theme === 'dark' ? '1px solid #2563eb' : '1px solid #3b82f6',
          },
        },
      }}
    />
  );
}
