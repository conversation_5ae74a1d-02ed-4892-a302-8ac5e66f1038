// Admin authentication utilities for BrimBag
import { supabase } from "../supabase";
import type { AuthUser } from "../auth";
import type { AdminProfile } from "../../types/admin";
import { isAdminRole } from "./adminUtils";
import { ADMIN_ACTION_TYPES, ADMIN_TARGET_TYPES } from "./adminConstants";

/**
 * Verify if current user has admin privileges
 */
export async function verifyAdminAccess(): Promise<{
  isAdmin: boolean;
  user: AuthUser | null;
  error?: string;
}> {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      return { isAdmin: false, user: null, error: error.message };
    }

    if (!user) {
      return { isAdmin: false, user: null, error: "Not authenticated" };
    }

    // Check role from user metadata first (faster)
    const roleFromMetadata = user.user_metadata?.role;
    if (roleFromMetadata === "admin") {
      const authUser: AuthUser = {
        ...user,
        profile: {
          full_name: user.user_metadata?.full_name || null,
          avatar_url: null,
          role: "admin",
          institution: user.user_metadata?.institution || null,
        },
      };
      return { isAdmin: true, user: authUser };
    }

    // Fallback: Get user profile to check role (avoid RLS issues by using direct query)
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    if (profileError) {
      // If profile fetch fails, check metadata as fallback
      const isAdmin = isAdminRole(roleFromMetadata);
      const authUser: AuthUser = {
        ...user,
        profile: {
          full_name: user.user_metadata?.full_name || null,
          avatar_url: null,
          role: roleFromMetadata || "student",
          institution: user.user_metadata?.institution || null,
        },
      };
      return { isAdmin, user: authUser };
    }

    const isAdmin = isAdminRole(profile?.role);

    const authUser: AuthUser = {
      ...user,
      profile: {
        full_name: profile?.full_name || null,
        avatar_url: profile?.avatar_url || null,
        role: profile?.role || "student",
        institution: profile?.institution || null,
      },
    };

    return { isAdmin, user: authUser };
  } catch (error: any) {
    return {
      isAdmin: false,
      user: null,
      error: error.message || "Failed to verify admin access",
    };
  }
}

/**
 * Get admin profile with enhanced information
 */
export async function getAdminProfile(userId: string): Promise<{
  profile: AdminProfile | null;
  error?: string;
}> {
  try {
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .eq("role", "admin")
      .single();

    if (error) {
      return { profile: null, error: error.message };
    }

    return { profile: profile as AdminProfile };
  } catch (error: any) {
    return {
      profile: null,
      error: error.message || "Failed to fetch admin profile",
    };
  }
}

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(
  actionType: string,
  targetType: "user" | "document" | "room" | "system",
  targetId?: string,
  details?: Record<string, any>
): Promise<{ success: boolean; error?: string; actionId?: string }> {
  try {
    const { data, error } = await supabase.rpc("log_admin_action", {
      p_action_type: actionType,
      p_target_type: targetType,
      p_target_id: targetId || null,
      p_details: details || null,
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, actionId: data };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to log admin action",
    };
  }
}

/**
 * Get admin action history
 */
export async function getAdminActionHistory(
  adminId?: string,
  limit: number = 50,
  offset: number = 0
): Promise<{
  actions: any[];
  total: number;
  error?: string;
}> {
  try {
    let query = supabase
      .from("admin_actions")
      .select(
        `
        *,
        profiles!admin_actions_admin_id_fkey(full_name)
      `,
        { count: "exact" }
      )
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (adminId) {
      query = query.eq("admin_id", adminId);
    }

    const { data, error, count } = await query;

    if (error) {
      return { actions: [], total: 0, error: error.message };
    }

    const actions = (data || []).map((action) => ({
      id: action.id,
      admin_id: action.admin_id,
      admin_name: action.profiles?.full_name || null,
      action_type: action.action_type,
      target_type: action.target_type,
      target_id: action.target_id,
      details: action.details,
      created_at: action.created_at,
    }));

    return { actions, total: count || 0 };
  } catch (error: any) {
    return {
      actions: [],
      total: 0,
      error: error.message || "Failed to fetch admin action history",
    };
  }
}

/**
 * Admin login with enhanced logging
 */
export async function adminLogin(
  email: string,
  password: string
): Promise<{
  success: boolean;
  user?: AuthUser | null;
  error?: string;
}> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return { success: false, error: error.message };
    }

    if (!data.user) {
      return { success: false, error: "Login failed" };
    }

    // Verify admin role
    const { isAdmin, user, error: verifyError } = await verifyAdminAccess();

    if (verifyError) {
      return { success: false, error: verifyError };
    }

    if (!isAdmin) {
      // Sign out non-admin user
      await supabase.auth.signOut();
      return {
        success: false,
        error: "Access denied. Admin privileges required.",
      };
    }

    // Log admin login
    await logAdminAction(
      ADMIN_ACTION_TYPES.LOGIN,
      ADMIN_TARGET_TYPES.SYSTEM,
      undefined,
      { login_time: new Date().toISOString() }
    );

    return { success: true, user };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Admin login failed",
    };
  }
}

/**
 * Admin logout with logging
 */
export async function adminLogout(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Log admin logout before signing out
    await logAdminAction(
      ADMIN_ACTION_TYPES.LOGOUT,
      ADMIN_TARGET_TYPES.SYSTEM,
      undefined,
      { logout_time: new Date().toISOString() }
    );

    const { error } = await supabase.auth.signOut();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Admin logout failed",
    };
  }
}

/**
 * Check if user can perform specific admin action
 */
export function canPerformAdminAction(
  userRole: string | undefined | null,
  _actionType?: string,
  _targetType?: string
): boolean {
  if (!isAdminRole(userRole as any)) {
    return false;
  }

  // For now, all admins can perform all actions
  // This can be extended for granular permissions later
  return true;
}

/**
 * Validate admin session
 */
export async function validateAdminSession(): Promise<{
  isValid: boolean;
  user?: AuthUser | null;
  error?: string;
}> {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      return { isValid: false, error: error.message };
    }

    if (!session) {
      return { isValid: false, error: "No active session" };
    }

    const { isAdmin, user, error: verifyError } = await verifyAdminAccess();

    if (verifyError) {
      return { isValid: false, error: verifyError };
    }

    if (!isAdmin) {
      return { isValid: false, error: "Admin privileges required" };
    }

    return { isValid: true, user };
  } catch (error: any) {
    return {
      isValid: false,
      error: error.message || "Session validation failed",
    };
  }
}

/**
 * Refresh admin session
 */
export async function refreshAdminSession(): Promise<{
  success: boolean;
  user?: AuthUser | null;
  error?: string;
}> {
  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      return { success: false, error: error.message };
    }

    if (!data.session) {
      return { success: false, error: "Failed to refresh session" };
    }

    const { isAdmin, user, error: verifyError } = await verifyAdminAccess();

    if (verifyError) {
      return { success: false, error: verifyError };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    return { success: true, user };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Session refresh failed",
    };
  }
}
