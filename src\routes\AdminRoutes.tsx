import { Suspense, lazy, Component } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { AdminRoute } from "../components/admin/AdminRoute";
import { AdminLoginForm } from "../components/admin/auth/AdminLoginForm";

// Lazy load admin components for code splitting
const AdminDashboard = lazy(() =>
  import("../pages/admin/AdminDashboard").then((module) => ({
    default: module.AdminDashboard,
  }))
);

const UserManagement = lazy(() =>
  import("../pages/admin/UserManagement").then((module) => ({
    default: module.UserManagement,
  }))
);

const DocumentManagement = lazy(() =>
  import("../pages/admin/DocumentManagement").then((module) => ({
    default: module.DocumentManagement,
  }))
);

const RoomManagement = lazy(() =>
  import("../pages/admin/RoomManagement").then((module) => ({
    default: module.RoomManagement,
  }))
);

const StorageManagement = lazy(() =>
  import("../pages/admin/StorageManagement").then((module) => ({
    default: module.StorageManagement,
  }))
);

const ActivityMonitoring = lazy(() =>
  import("../pages/admin/ActivityMonitoring").then((module) => ({
    default: module.ActivityMonitoring,
  }))
);

const SystemSettings = lazy(() =>
  import("../pages/admin/SystemSettings").then((module) => ({
    default: module.SystemSettings,
  }))
);

const FeedbackManagement = lazy(() =>
  import("../pages/admin/FeedbackManagement").then((module) => ({
    default: module.FeedbackManagement,
  }))
);

// Loading component for admin pages
function AdminLoadingSpinner() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
          Loading admin panel...
        </p>
      </div>
    </div>
  );
}

// Error boundary for admin routes
class AdminErrorBoundary extends Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Admin panel error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
          <div className="max-w-md w-full bg-white dark:bg-dark-800 rounded-lg shadow-lg p-8 text-center transition-theme duration-theme">
            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
              <svg
                className="w-8 h-8 text-red-600 dark:text-red-400 transition-theme duration-theme"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
              Admin Panel Error
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
              Something went wrong while loading the admin panel. Please try
              refreshing the page.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
              >
                Refresh Page
              </button>
              <button
                onClick={() => (window.location.href = "/dashboard")}
                className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-all duration-200"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export function AdminRoutes() {
  return (
    <AdminErrorBoundary>
      <Routes>
        {/* Public admin login route */}
        <Route path="/login" element={<AdminLoginForm />} />

        {/* Protected admin routes */}
        <Route
          path="/dashboard"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <AdminDashboard />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/users"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <UserManagement />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/documents"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <DocumentManagement />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/rooms"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <RoomManagement />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/storage"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <StorageManagement />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/activity"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <ActivityMonitoring />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/feedback"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <FeedbackManagement />
              </Suspense>
            </AdminRoute>
          }
        />

        <Route
          path="/settings"
          element={
            <AdminRoute>
              <Suspense fallback={<AdminLoadingSpinner />}>
                <SystemSettings />
              </Suspense>
            </AdminRoute>
          }
        />

        {/* Default redirect to dashboard */}
        <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </AdminErrorBoundary>
  );
}
