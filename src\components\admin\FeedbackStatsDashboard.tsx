import { useState, useEffect } from "react";
import { feedbackService } from "../../lib/feedbackService";
import type { FeedbackStats } from "../../lib/feedbackService";

interface FeedbackStatsDashboardProps {
  onStatsLoad?: (stats: FeedbackStats) => void;
}

export function FeedbackStatsDashboard({ onStatsLoad }: FeedbackStatsDashboardProps) {
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      const statsData = await feedbackService.getFeedbackStats();
      setStats(statsData);
      onStatsLoad?.(statsData);
    } catch (error: any) {
      console.error("Failed to load feedback stats:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getProgressPercentage = (value: number, total: number) => {
    return total > 0 ? (value / total) * 100 : 0;
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600 dark:text-green-400";
    if (rating >= 3.5) return "text-yellow-600 dark:text-yellow-400";
    if (rating >= 2.5) return "text-orange-600 dark:text-orange-400";
    return "text-red-600 dark:text-red-400";
  };

  const getRatingBgColor = (rating: number) => {
    if (rating >= 4.5) return "bg-green-100 dark:bg-green-900/30";
    if (rating >= 3.5) return "bg-yellow-100 dark:bg-yellow-900/30";
    if (rating >= 2.5) return "bg-orange-100 dark:bg-orange-900/30";
    return "bg-red-100 dark:bg-red-900/30";
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-dark-600 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 dark:bg-dark-600 rounded w-1/2 mb-4"></div>
              <div className="h-2 bg-gray-200 dark:bg-dark-600 rounded w-full"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400">Failed to load feedback statistics</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">📝</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Feedback</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalFeedback}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-sm font-medium">⏳</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.pendingFeedback}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <span className="text-green-600 dark:text-green-400 text-sm font-medium">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Resolved</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.resolvedFeedback}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 ${getRatingBgColor(stats.averageRating)} rounded-lg flex items-center justify-center`}>
                <span className={`${getRatingColor(stats.averageRating)} text-sm font-medium`}>⭐</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Rating</p>
              <p className={`text-2xl font-bold ${getRatingColor(stats.averageRating)}`}>
                {stats.averageRating.toFixed(1)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Category Breakdown */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Feedback by Category
          </h3>
          <div className="space-y-4">
            {Object.entries(stats.categoryBreakdown).map(([category, count]) => {
              const percentage = getProgressPercentage(count, stats.totalFeedback);
              const categoryLabels = {
                bug_report: "Bug Reports",
                feature_request: "Feature Requests",
                general: "General Feedback",
                ui_ux: "UI/UX Issues",
              };
              const categoryColors = {
                bug_report: "bg-red-500",
                feature_request: "bg-purple-500",
                general: "bg-gray-500",
                ui_ux: "bg-indigo-500",
              };
              
              return (
                <div key={category}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {categoryLabels[category as keyof typeof categoryLabels]}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {count} ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${categoryColors[category as keyof typeof categoryColors]}`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Rating Distribution */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Rating Distribution
          </h3>
          <div className="space-y-4">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown];
              const percentage = getProgressPercentage(count, stats.totalFeedback);
              
              return (
                <div key={rating}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {rating} Star{rating !== 1 ? 's' : ''}
                      </span>
                      <div className="flex items-center">
                        {[...Array(rating)].map((_, i) => (
                          <svg key={i} className="w-3 h-3 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {count} ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                    <div
                      className="h-2 rounded-full bg-yellow-500"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Status Progress */}
      <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
          Feedback Status Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-yellow-600 dark:text-yellow-400 text-xl">⏳</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.pendingFeedback}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Pending Review</p>
            <div className="mt-2 w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-yellow-500"
                style={{ width: `${getProgressPercentage(stats.pendingFeedback, stats.totalFeedback)}%` }}
              ></div>
            </div>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-blue-600 dark:text-blue-400 text-xl">👀</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.reviewedFeedback}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Under Review</p>
            <div className="mt-2 w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-blue-500"
                style={{ width: `${getProgressPercentage(stats.reviewedFeedback, stats.totalFeedback)}%` }}
              ></div>
            </div>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-green-600 dark:text-green-400 text-xl">✅</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.resolvedFeedback}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Resolved</p>
            <div className="mt-2 w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-green-500"
                style={{ width: `${getProgressPercentage(stats.resolvedFeedback, stats.totalFeedback)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
