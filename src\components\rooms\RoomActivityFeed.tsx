import { useState, useEffect } from "react";
import { supabase } from "../../lib/supabase";
import { formatDateTime } from "../../lib/utils";

interface ActivityItem {
  id: string;
  type: "document_shared" | "member_joined" | "room_created";
  user_name: string;
  user_email: string;
  details: string;
  created_at: string;
}

interface RoomActivityFeedProps {
  roomId: string;
  limit?: number;
}

export function RoomActivityFeed({
  roomId,
  limit = 10,
}: RoomActivityFeedProps) {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadActivities();
  }, [roomId]);

  const loadActivities = async () => {
    try {
      setIsLoading(true);

      // Get recent document shares
      const { data: documentShares, error: docError } = await supabase
        .from("room_documents")
        .select(
          `
          id,
          shared_at,
          shared_by,
          documents(title)
        `
        )
        .eq("room_id", roomId)
        .order("shared_at", { ascending: false })
        .limit(limit);

      if (docError) throw docError;

      // Get recent member joins
      const { data: memberJoins, error: memberError } = await supabase
        .from("room_members")
        .select(
          `
          id,
          joined_at,
          user_id
        `
        )
        .eq("room_id", roomId)
        .order("joined_at", { ascending: false })
        .limit(limit);

      if (memberError) throw memberError;

      // Combine and format activities
      const combinedActivities: ActivityItem[] = [];

      // Add document shares with profile information
      if (documentShares) {
        for (const share of documentShares) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("full_name, email")
            .eq("id", share.shared_by)
            .single();

          combinedActivities.push({
            id: `doc-${share.id}`,
            type: "document_shared",
            user_name: profile?.full_name || profile?.email || "Unknown User",
            user_email: profile?.email || "",
            details: (share.documents as any)?.title || "Unknown Document",
            created_at: share.shared_at,
          });
        }
      }

      // Add member joins with profile information
      if (memberJoins) {
        for (const member of memberJoins) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("full_name, email")
            .eq("id", member.user_id)
            .single();

          combinedActivities.push({
            id: `member-${member.id}`,
            type: "member_joined",
            user_name: profile?.full_name || profile?.email || "Unknown User",
            user_email: profile?.email || "",
            details: "",
            created_at: member.joined_at,
          });
        }
      }

      // Sort by date and limit
      combinedActivities.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      setActivities(combinedActivities.slice(0, limit));
    } catch (error: any) {
      console.error("Load activities error:", error);
      setActivities([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "document_shared":
        return (
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
            <svg
              className="w-4 h-4 text-blue-600 dark:text-blue-400 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
        );
      case "member_joined":
        return (
          <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
            <svg
              className="w-4 h-4 text-green-600 dark:text-green-400 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
        );
      case "room_created":
        return (
          <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
            <svg
              className="w-4 h-4 text-purple-600 dark:text-purple-400 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 dark:bg-dark-700 rounded-full flex items-center justify-center transition-theme duration-theme">
            <svg
              className="w-4 h-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
    }
  };

  const getActivityText = (activity: ActivityItem) => {
    switch (activity.type) {
      case "document_shared":
        return (
          <span>
            <span className="font-medium">{activity.user_name}</span> shared{" "}
            <span className="font-medium text-blue-600">
              {activity.details}
            </span>
          </span>
        );
      case "member_joined":
        return (
          <span>
            <span className="font-medium">{activity.user_name}</span> joined the
            room
          </span>
        );
      case "room_created":
        return (
          <span>
            <span className="font-medium">{activity.user_name}</span> created
            the room
          </span>
        );
      default:
        return <span>Unknown activity</span>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3 animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">📝</div>
        <p className="text-gray-500 text-sm">No recent activity</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-3">
          {getActivityIcon(activity.type)}
          <div className="flex-1 min-w-0">
            <p className="text-sm text-gray-900 dark:text-gray-100 transition-theme duration-theme">
              {getActivityText(activity)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 transition-theme duration-theme">
              {formatDateTime(activity.created_at)}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
