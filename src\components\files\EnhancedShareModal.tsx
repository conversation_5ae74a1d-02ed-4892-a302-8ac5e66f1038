import { useState } from "react";
import type { DocumentFile } from "../../lib/fileService";
import { ShareToRoom } from "./ShareToRoom";
import { ShareToUser } from "./ShareToUser";
import { ShareManagement } from "./ShareManagement";

interface EnhancedShareModalProps {
  document: DocumentFile;
  onClose: () => void;
  onShareComplete?: () => void;
}

type ShareTab = "rooms" | "users" | "manage";

export function EnhancedShareModal({
  document,
  onClose,
  onShareComplete,
}: EnhancedShareModalProps) {
  const [activeTab, setActiveTab] = useState<ShareTab | null>(null);

  const handleShareComplete = () => {
    onShareComplete?.();
    // Switch to manage tab to show the new shares
    setActiveTab("manage");
  };

  const tabs = [
    {
      id: "users" as ShareTab,
      name: "Share with Users",
      icon: (
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      ),
      description: "Share directly with individual users",
    },
    {
      id: "rooms" as ShareTab,
      name: "Share to Rooms",
      icon: (
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
      ),
      description: "Share with all members of a room",
    },
    // {
    //   id: "manage" as ShareTab,
    //   name: "Manage Shares",
    //   icon: (
    //     <svg
    //       className="w-4 h-4"
    //       fill="none"
    //       stroke="currentColor"
    //       viewBox="0 0 24 24"
    //     >
    //       <path
    //         strokeLinecap="round"
    //         strokeLinejoin="round"
    //         strokeWidth={2}
    //         d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
    //       />
    //       <path
    //         strokeLinecap="round"
    //         strokeLinejoin="round"
    //         strokeWidth={2}
    //         d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    //       />
    //     </svg>
    //   ),
    //   description: "View and manage existing shares",
    // },
  ];

  // If a specific tab is selected, render the corresponding component
  if (activeTab === "rooms") {
    return (
      <ShareToRoom
        document={document}
        onClose={onClose}
        onShareComplete={handleShareComplete}
      />
    );
  }

  if (activeTab === "users") {
    return (
      <ShareToUser
        document={document}
        onClose={onClose}
        onShareComplete={handleShareComplete}
      />
    );
  }

  if (activeTab === "manage") {
    return (
      <ShareManagement
        document={document}
        onClose={onClose}
        onSharesUpdated={onShareComplete}
      />
    );
  }

  // Default: Show tab selector interface
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Share Document
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Document info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-1">{document.title}</h3>
            <p className="text-sm text-gray-600">
              {document.file_type.toUpperCase()} •{" "}
              {Math.round(document.file_size / 1024)} KB
            </p>
          </div>

          {/* Sharing Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className="border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                onClick={() => setActiveTab(tab.id)}
              >
                <div className="text-center">
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4 text-blue-600">
                    {tab.icon}
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {tab.name}
                  </h3>
                  <p className="text-sm text-gray-500 mb-4">
                    {tab.description}
                  </p>
                  <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full justify-center">
                    {tab.id === "users"
                      ? "Share with Users"
                      : tab.id === "rooms"
                      ? "Share to Rooms"
                      : "Manage Shares"}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
