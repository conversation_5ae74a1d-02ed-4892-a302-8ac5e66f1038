import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { XMarkIcon, StarIcon } from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import toast from "react-hot-toast";
import { feedbackService } from "../../lib/feedbackService";
import { formatDateTime } from "../../lib/utils";
import type {
  FeedbackWithProfile,
  FeedbackStatus,
} from "../../lib/feedbackService";

const adminNotesSchema = z.object({
  admin_notes: z
    .string()
    .max(1000, "Notes must be less than 1000 characters")
    .optional(),
  status: z.enum(["pending", "reviewed", "resolved"]),
});

type AdminNotesFormData = z.infer<typeof adminNotesSchema>;

interface FeedbackDetailsModalProps {
  feedback: FeedbackWithProfile;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

const statusLabels: Record<FeedbackStatus, string> = {
  pending: "Pending",
  reviewed: "Reviewed",
  resolved: "Resolved",
};

const statusColors: Record<FeedbackStatus, string> = {
  pending:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  reviewed: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  resolved:
    "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
};

const categoryLabels = {
  bug_report: "Bug Report",
  feature_request: "Feature Request",
  general: "General Feedback",
  ui_ux: "UI/UX Issue",
};

const categoryColors = {
  bug_report: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
  feature_request:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
  general: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
  ui_ux:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
};

export function FeedbackDetailsModal({
  feedback,
  isOpen,
  onClose,
  onUpdate,
}: FeedbackDetailsModalProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<AdminNotesFormData>({
    resolver: zodResolver(adminNotesSchema),
    defaultValues: {
      admin_notes: feedback.admin_notes || "",
      status: feedback.status,
    },
  });

  const watchedNotes = watch("admin_notes", "");
  const remainingChars = 1000 - (watchedNotes?.length || 0);

  const onSubmit = async (data: AdminNotesFormData) => {
    try {
      setIsUpdating(true);

      await feedbackService.updateFeedback(feedback.id, {
        status: data.status,
        admin_notes: data.admin_notes || undefined,
      });

      toast.success("Feedback updated successfully");
      onUpdate();
      onClose();
    } catch (error: any) {
      console.error("Failed to update feedback:", error);
      toast.error("Unable to update feedback. Please try again later.");
    } finally {
      setIsUpdating(false);
    }
  };

  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => {
          const StarComponent = star <= rating ? StarIconSolid : StarIcon;
          return (
            <StarComponent
              key={star}
              className={`w-5 h-5 ${
                star <= rating
                  ? "text-yellow-400"
                  : "text-gray-300 dark:text-gray-600"
              }`}
            />
          );
        })}
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
          {rating}/5
        </span>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white dark:bg-dark-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden transition-theme duration-theme">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">💬</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  Feedback Details
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Submitted by{" "}
                  {feedback.profiles?.full_name || "Anonymous User"}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              disabled={isUpdating}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors disabled:opacity-50"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Feedback Information */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Feedback Information
                  </h3>

                  {/* User Info */}
                  <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {feedback.profiles?.full_name?.charAt(0) ||
                            feedback.profiles?.email?.charAt(0).toUpperCase() ||
                            "U"}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {feedback.profiles?.full_name || "Anonymous User"}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {feedback.profiles?.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Metadata */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Submitted:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {formatDateTime(feedback.created_at)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Rating:
                      </span>
                      <div>{renderStarRating(feedback.rating)}</div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Category:
                      </span>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          categoryColors[feedback.category]
                        }`}
                      >
                        {categoryLabels[feedback.category]}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Status:
                      </span>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          statusColors[feedback.status]
                        }`}
                      >
                        {statusLabels[feedback.status]}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Feedback Text */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Feedback Message
                  </h4>
                  <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {feedback.feedback_text}
                    </p>
                  </div>
                </div>
              </div>

              {/* Admin Actions */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Admin Actions
                </h3>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Status Update */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Status
                    </label>
                    <select
                      {...register("status")}
                      disabled={isUpdating}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
                    >
                      <option value="pending">Pending</option>
                      <option value="reviewed">Reviewed</option>
                      <option value="resolved">Resolved</option>
                    </select>
                    {errors.status && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.status.message}
                      </p>
                    )}
                  </div>

                  {/* Admin Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Admin Notes
                    </label>
                    <textarea
                      {...register("admin_notes")}
                      rows={6}
                      disabled={isUpdating}
                      placeholder="Add internal notes about this feedback..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 disabled:opacity-50 resize-none"
                    />
                    <div className="flex justify-between items-center mt-2">
                      <div>
                        {errors.admin_notes && (
                          <p className="text-sm text-red-600 dark:text-red-400">
                            {errors.admin_notes.message}
                          </p>
                        )}
                      </div>
                      <p
                        className={`text-xs ${
                          remainingChars < 100
                            ? "text-orange-600 dark:text-orange-400"
                            : remainingChars < 50
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {remainingChars} characters remaining
                      </p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-dark-700">
                    <button
                      type="button"
                      onClick={onClose}
                      disabled={isUpdating}
                      className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-700 border border-gray-300 dark:border-dark-600 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {isUpdating ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Updating...</span>
                        </>
                      ) : (
                        <span>Update Feedback</span>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
