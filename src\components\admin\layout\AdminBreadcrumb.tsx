import { Link, useLocation } from "react-router-dom";
import { ADMIN_ROUTES } from "../../../lib/admin/adminConstants";

interface BreadcrumbItem {
  name: string;
  href?: string;
  current: boolean;
}

export function AdminBreadcrumb() {
  const location = useLocation();

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const path = location.pathname;
    const breadcrumbs: BreadcrumbItem[] = [
      { name: "Admin", href: ADMIN_ROUTES.DASHBOARD, current: false },
    ];

    // Map paths to breadcrumb items
    const pathMap: Record<string, string> = {
      [ADMIN_ROUTES.DASHBOARD]: "Dashboard",
      [ADMIN_ROUTES.USERS]: "Users",
      [ADMIN_ROUTES.DOCUMENTS]: "Documents",
      [ADMIN_ROUTES.ROOMS]: "Rooms",
      [ADMIN_ROUTES.STORAGE]: "Storage",
      [ADMIN_ROUTES.ACTIVITY]: "Activity",
      [ADMIN_ROUTES.FEEDBACK]: "User Feedback",
      [ADMIN_ROUTES.SETTINGS]: "Settings",
    };

    const currentPageName = pathMap[path];
    if (currentPageName && path !== ADMIN_ROUTES.DASHBOARD) {
      breadcrumbs.push({ name: currentPageName, current: true });
    } else if (path === ADMIN_ROUTES.DASHBOARD) {
      breadcrumbs[0].current = true;
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <nav className="flex py-4" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((item, index) => (
          <li key={item.name} className="flex items-center">
            {index > 0 && (
              <svg
                className="flex-shrink-0 h-4 w-4 text-gray-400 dark:text-gray-500 mx-2 transition-theme duration-theme"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}

            {item.current ? (
              <span className="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                {index === 0 && (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                )}
                {item.name}
              </span>
            ) : (
              <Link
                to={item.href!}
                className="flex items-center text-sm font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-all duration-200 hover:underline"
              >
                {index === 0 && (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                )}
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
