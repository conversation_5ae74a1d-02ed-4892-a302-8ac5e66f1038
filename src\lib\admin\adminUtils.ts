// Admin utility functions for SmartBagPack
import type { UserRole } from "../../types/shared";
import type { AdminPermission, SystemStats } from "../../types/admin";

/**
 * Check if a user role has admin privileges
 */
export function isAdminRole(role: UserRole | undefined | null): boolean {
  return role === "admin";
}

/**
 * Check if a user has specific admin permission
 */
export function hasAdminPermission(
  userRole: UserRole | undefined | null,
  _permission?: AdminPermission
): boolean {
  if (!isAdminRole(userRole)) {
    return false;
  }

  // For now, all admins have all permissions
  // This can be extended later for granular permissions
  return true;
}

/**
 * Get admin permissions for a user role
 */
export function getAdminPermissions(
  role: UserRole | undefined | null
): AdminPermission[] {
  if (!isAdminRole(role)) {
    return [];
  }

  // Return all permissions for admin users
  return [
    "view_users",
    "manage_users",
    "view_documents",
    "manage_documents",
    "view_rooms",
    "manage_rooms",
    "view_analytics",
    "system_settings",
    "view_logs",
  ];
}

/**
 * Format admin action type for display
 */
export function formatAdminActionType(actionType: string): string {
  const actionMap: Record<string, string> = {
    user_role_change: "Changed User Role",
    user_deactivate: "Deactivated User",
    user_activate: "Activated User",
    document_delete: "Deleted Document",
    room_archive: "Archived Room",
    room_delete: "Deleted Room",
    system_setting_change: "Changed System Setting",
    storage_cleanup: "Storage Cleanup",
    bulk_action: "Bulk Action",
  };

  return (
    actionMap[actionType] ||
    actionType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  );
}

/**
 * Calculate system health score based on stats
 */
export function calculateSystemHealth(stats: SystemStats): {
  score: number;
  status: "excellent" | "good" | "warning" | "critical";
  issues: string[];
} {
  let score = 100;
  const issues: string[] = [];

  // Check storage usage
  if (stats.storage.usage_percentage > 90) {
    score -= 20;
    issues.push("Storage usage is critically high");
  } else if (stats.storage.usage_percentage > 75) {
    score -= 10;
    issues.push("Storage usage is high");
  }

  // Check user activity (if no new users in 30 days)
  if (stats.users.new_users_30d === 0 && stats.users.total_users > 0) {
    score -= 15;
    issues.push("No new user registrations in 30 days");
  }

  // Check room activity
  if (stats.rooms.active_rooms_7d < stats.rooms.total_rooms * 0.1) {
    score -= 10;
    issues.push("Low room activity");
  }

  // Determine status
  let status: "excellent" | "good" | "warning" | "critical";
  if (score >= 90) status = "excellent";
  else if (score >= 75) status = "good";
  else if (score >= 50) status = "warning";
  else status = "critical";

  return { score, status, issues };
}

/**
 * Format file size for admin display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Format percentage for display
 */
export function formatPercentage(value: number, total: number): string {
  if (total === 0) return "0%";
  return Math.round((value / total) * 100) + "%";
}

/**
 * Format date for admin display
 */
export function formatAdminDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

/**
 * Format relative time for admin display
 */
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 2592000)
    return `${Math.floor(diffInSeconds / 86400)}d ago`;

  return formatAdminDate(dateString);
}

/**
 * Generate admin action description
 */
export function generateActionDescription(
  actionType: string,
  _targetType?: string,
  _details?: Record<string, any> | null
): string {
  const baseAction = formatAdminActionType(actionType);

  if (!_details) return baseAction;

  switch (actionType) {
    case "user_role_change":
      return `${baseAction}: ${_details.old_role} → ${_details.new_role}`;
    case "document_delete":
      return `${baseAction}: ${_details.document_name || "Unknown document"}`;
    case "room_archive":
      return `${baseAction}: ${_details.room_name || "Unknown room"}`;
    case "bulk_action":
      return `${baseAction}: ${_details.action} on ${_details.count} items`;
    default:
      return baseAction;
  }
}

/**
 * Validate admin action input
 */
export function validateAdminAction(
  actionType: string,
  targetType: string,
  targetId: string | null,
  _details?: Record<string, any> | null
): { isValid: boolean; error?: string } {
  if (!actionType.trim()) {
    return { isValid: false, error: "Action type is required" };
  }

  if (!targetType.trim()) {
    return { isValid: false, error: "Target type is required" };
  }

  const validTargetTypes = ["user", "document", "room", "system"];
  if (!validTargetTypes.includes(targetType)) {
    return { isValid: false, error: "Invalid target type" };
  }

  // For non-system actions, target ID is required
  if (targetType !== "system" && !targetId) {
    return {
      isValid: false,
      error: "Target ID is required for this action type",
    };
  }

  return { isValid: true };
}

/**
 * Get color class for system status
 */
export function getStatusColorClass(
  status: "excellent" | "good" | "warning" | "critical"
): string {
  const colorMap = {
    excellent: "text-green-600 dark:text-green-400",
    good: "text-blue-600 dark:text-blue-400",
    warning: "text-yellow-600 dark:text-yellow-400",
    critical: "text-red-600 dark:text-red-400",
  };

  return colorMap[status];
}

/**
 * Get background color class for system status
 */
export function getStatusBgColorClass(
  status: "excellent" | "good" | "warning" | "critical"
): string {
  const colorMap = {
    excellent: "bg-green-100 dark:bg-green-900/30",
    good: "bg-blue-100 dark:bg-blue-900/30",
    warning: "bg-yellow-100 dark:bg-yellow-900/30",
    critical: "bg-red-100 dark:bg-red-900/30",
  };

  return colorMap[status];
}

/**
 * Debounce function for admin search
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Generate CSV content from data
 */
export function generateCSV<T extends Record<string, any>>(
  data: T[],
  headers: { key: keyof T; label: string }[]
): string {
  const csvHeaders = headers.map((h) => h.label).join(",");
  const csvRows = data.map((row) =>
    headers
      .map((h) => {
        const value = row[h.key];
        // Escape commas and quotes in CSV
        if (
          typeof value === "string" &&
          (value.includes(",") || value.includes('"'))
        ) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || "";
      })
      .join(",")
  );

  return [csvHeaders, ...csvRows].join("\n");
}

/**
 * Download CSV file
 */
export function downloadCSV(content: string, filename: string): void {
  const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
