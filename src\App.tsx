import React, { Suspense } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { ThemeAwareToaster } from "./components/ui/ThemeAwareToaster";
import { PWAInstallPrompt } from "./components/ui/PWAInstallPrompt";
import { ProtectedRoute, PublicRoute } from "./components/ProtectedRoute";
import { LandingPage } from "./components/LandingPage";
import { LoginForm } from "./components/auth/LoginForm";
import { RegisterForm } from "./components/auth/RegisterForm";
import { ForgotPasswordForm } from "./components/auth/ForgotPasswordForm";
import { ResetPasswordForm } from "./components/auth/ResetPasswordForm";
import { Dashboard } from "./components/Dashboard";
import { Documents } from "./pages/Documents";
import { Upload } from "./pages/Upload";
import { Rooms } from "./pages/Rooms";
import { RoomDetail } from "./pages/RoomDetail";
import { SharedDocuments } from "./pages/SharedDocuments";
import { Profile } from "./pages/Profile";
import { FeedbackPage } from "./pages/FeedbackPage";
import { Notifications } from "./pages/Notifications";
import { Invitations } from "./pages/Invitations";
import { JoinRoomByLink } from "./pages/JoinRoomByLink";

// Lazy load admin routes for code splitting
const AdminRoutes = React.lazy(() =>
  import("./routes/AdminRoutes").then((module) => ({
    default: module.AdminRoutes,
  }))
);

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <LoginForm />
                  </PublicRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <PublicRoute>
                    <RegisterForm />
                  </PublicRoute>
                }
              />
              <Route
                path="/forgot-password"
                element={
                  <PublicRoute>
                    <ForgotPasswordForm />
                  </PublicRoute>
                }
              />
              <Route
                path="/forgot-password/reset"
                element={
                  <PublicRoute>
                    <ResetPasswordForm />
                  </PublicRoute>
                }
              />

              {/* Protected routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/documents"
                element={
                  <ProtectedRoute>
                    <Documents />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/upload"
                element={
                  <ProtectedRoute>
                    <Upload />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/rooms"
                element={
                  <ProtectedRoute>
                    <Rooms />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/rooms/:roomId"
                element={
                  <ProtectedRoute>
                    <RoomDetail />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shared"
                element={
                  <ProtectedRoute>
                    <SharedDocuments />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/feedback"
                element={
                  <ProtectedRoute>
                    <FeedbackPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/notifications"
                element={
                  <ProtectedRoute>
                    <Notifications />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/invitations"
                element={
                  <ProtectedRoute>
                    <Invitations />
                  </ProtectedRoute>
                }
              />

              {/* Public join room route */}
              <Route path="/join-room/:token" element={<JoinRoomByLink />} />

              {/* Admin routes with lazy loading */}
              <Route
                path="/admin/*"
                element={
                  <Suspense
                    fallback={
                      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                            Loading admin panel...
                          </p>
                        </div>
                      </div>
                    }
                  >
                    <AdminRoutes />
                  </Suspense>
                }
              />

              {/* Landing page for non-authenticated users */}
              <Route
                path="/"
                element={
                  <PublicRoute>
                    <LandingPage />
                  </PublicRoute>
                }
              />

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>

            {/* Toast notifications */}
            <ThemeAwareToaster />

            {/* PWA Install Prompt */}
            <PWAInstallPrompt />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
