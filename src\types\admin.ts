// Admin-specific type definitions for BrimBag
import type { Database } from "../lib/supabase";

// Base types from database
export type AdminProfile = Database["public"]["Tables"]["profiles"]["Row"] & {
  role: "admin";
};

export type UserProfile = Database["public"]["Tables"]["profiles"]["Row"];
export type DocumentRow = Database["public"]["Tables"]["documents"]["Row"];
export type RoomRow = Database["public"]["Tables"]["rooms"]["Row"];

// Admin-specific interfaces
export interface SystemStats {
  users: {
    total_users: number;
    new_users_30d: number;
    admin_count: number;
    lecturer_count: number;
    student_count: number;
    active_users_7d: number;
  };
  documents: {
    total_documents: number;
    total_storage_used: number;
    recent_uploads: number;
    active_uploaders: number;
    avg_file_size: number;
  };
  rooms: {
    total_rooms: number;
    public_rooms: number;
    private_rooms: number;
    new_rooms_30d: number;
    avg_members_per_room: number;
    active_rooms_7d: number;
  };
  storage: {
    total_used: number;
    total_capacity: number;
    avg_per_user: number;
    usage_percentage: number;
  };
}

export interface AdminUser extends UserProfile {
  user_stats: {
    document_count: number;
    storage_used: number;
    rooms_joined: number;
    rooms_created: number;
    documents_shared_with_user?: number;
    recent_uploads_7d?: number;
    last_activity: string | null;
    registration_date: string;
  };
}

export interface AdminDocument extends DocumentRow {
  owner: {
    id: string;
    full_name: string | null;
    email: string;
  };
  sharing_info: {
    is_shared: boolean;
    shared_count: number;
    room_count: number;
  };
}

export interface AdminRoom extends RoomRow {
  creator: {
    id: string;
    full_name: string | null;
    email: string;
  };
  stats: {
    member_count: number;
    document_count: number;
    storage_used: number;
    activity_score: number;
  };
}

export interface ActivityEvent {
  id: string;
  user_id: string;
  user_name: string | null;
  user_email: string;
  action_type:
    | "login"
    | "logout"
    | "upload"
    | "share"
    | "join_room"
    | "create_room"
    | "delete_document"
    | "admin_action";
  target_type: "user" | "document" | "room" | "system" | null;
  target_id: string | null;
  details: Record<string, any> | null;
  created_at: string;
}

export interface AdminAction {
  id: string;
  admin_id: string;
  admin_name: string | null;
  action_type: string;
  target_type: "user" | "document" | "room" | "system";
  target_id: string | null;
  details: Record<string, any> | null;
  created_at: string;
}

export interface StorageAnalytics {
  total_usage: number;
  capacity: number;
  usage_by_type: {
    pdf: number;
    images: number;
    documents: number;
    presentations: number;
    spreadsheets: number;
    other: number;
  };
  usage_by_user: Array<{
    user_id: string;
    user_name: string | null;
    storage_used: number;
    document_count: number;
  }>;
  growth_trend: Array<{
    date: string;
    total_usage: number;
    new_uploads: number;
  }>;
}

// Bulk operation result types
export interface BulkOperationResult {
  success: number;
  failed: number;
  errors: string[];
}

// Filter and pagination types
export interface UserFilters {
  registration_date_from?: string;
  registration_date_to?: string;
  last_activity_from?: string;
  last_activity_to?: string;
  search?: string;
}

export interface DocumentFilters {
  file_type?: string;
  owner_id?: string;
  upload_date_from?: string;
  upload_date_to?: string;
  min_size?: number;
  max_size?: number;
  is_shared?: boolean;
  search?: string;
}

export interface RoomFilters {
  is_private?: boolean;
  creator_id?: string;
  created_date_from?: string;
  created_date_to?: string;
  min_members?: number;
  max_members?: number;
  search?: string;
}

export interface ActivityFilters {
  user_id?: string;
  action_type?: ActivityEvent["action_type"];
  target_type?: ActivityEvent["target_type"];
  date_from?: string;
  date_to?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Admin route and permission types
export type AdminPermission =
  | "view_users"
  | "manage_users"
  | "view_documents"
  | "manage_documents"
  | "view_rooms"
  | "manage_rooms"
  | "view_analytics"
  | "view_feedback"
  | "manage_feedback"
  | "system_settings"
  | "view_logs";

export interface AdminRouteConfig {
  path: string;
  name: string;
  icon: React.ReactNode;
  permissions: AdminPermission[];
  component: React.ComponentType;
}
