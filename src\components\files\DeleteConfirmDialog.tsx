import type { DocumentFile } from "../../lib/fileService";
import { formatFileSize } from "../../lib/utils";

interface DeleteConfirmDialogProps {
  documents: DocumentFile[];
  onConfirm: () => void;
  onCancel: () => void;
  isDeleting?: boolean;
}

export function DeleteConfirmDialog({
  documents,
  onConfirm,
  onCancel,
  isDeleting = false,
}: DeleteConfirmDialogProps) {
  const isMultiple = documents.length > 1;
  const totalSize = documents.reduce((sum, doc) => sum + doc.file_size, 0);

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              {isMultiple ? "Delete Documents" : "Delete Document"}
            </h3>
            <button
              onClick={onCancel}
              disabled={isDeleting}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="px-6 py-4">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                Are you sure you want to delete{" "}
                {isMultiple ? "these documents" : "this document"}?
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                This action cannot be undone.{" "}
                {isMultiple ? "The documents" : "The document"} will be
                permanently removed from your account and storage.
              </p>

              {/* Document List */}
              <div className="bg-gray-50 rounded-lg p-4 max-h-48 overflow-y-auto">
                {isMultiple ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
                      <span>Documents to delete ({documents.length})</span>
                      <span>Total size: {formatFileSize(totalSize)}</span>
                    </div>
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center space-x-3 py-1"
                      >
                        <span className="text-lg">📄</span>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {doc.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {doc.file_type} • {formatFileSize(doc.file_size)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">📄</span>
                    <div>
                      <p className="font-medium text-gray-900">
                        {documents[0].title}
                      </p>
                      <p className="text-sm text-gray-600">
                        {documents[0].file_type}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(documents[0].file_size)}
                      </p>
                      {documents[0].description && (
                        <p className="text-sm text-gray-500 mt-1">
                          {documents[0].description}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Warning */}
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <svg
                    className="w-5 h-5 text-yellow-600 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Warning:</p>
                    <p>
                      {isMultiple ? "These files" : "This file"} will be removed
                      from all rooms and shares. Other users will no longer be
                      able to access {isMultiple ? "them" : "it"}.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onCancel}
            disabled={isDeleting}
            className="btn-secondary disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
                <span>
                  Delete{" "}
                  {isMultiple ? `${documents.length} Documents` : "Document"}
                </span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
