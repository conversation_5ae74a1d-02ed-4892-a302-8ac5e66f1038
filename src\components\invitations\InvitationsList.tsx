import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { invitationService } from "../../lib/invitationService";
import { notificationService } from "../../lib/notificationService";
import type { InvitationWithDetails } from "../../lib/invitationService";
import { useAuth } from "../../contexts/AuthContext";
import { formatDateTime } from "../../lib/utils";

interface InvitationsListProps {
  onInvitationUpdate?: () => void;
}

export function InvitationsList({ onInvitationUpdate }: InvitationsListProps) {
  const { user } = useAuth();
  const [invitations, setInvitations] = useState<InvitationWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (user) {
      loadInvitations();
    }
  }, [user]);

  const loadInvitations = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userInvitations = await invitationService.getUserInvitations(
        user.id
      );
      setInvitations(userInvitations);
    } catch (error: any) {
      console.error("Failed to load invitations:", error);
      toast.error("Failed to load invitations");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptInvitation = async (invitation: InvitationWithDetails) => {
    if (!user) return;

    try {
      setProcessingIds((prev) => new Set(prev).add(invitation.id));

      await invitationService.acceptInvitation(invitation.id, user.id);

      // Notify room admin about new member
      await notificationService.notifyRoomJoin({
        roomAdminId: invitation.invited_by, // The person who sent the invitation
        memberName: user.profile?.full_name || user.email || "Someone",
        roomName: invitation.room.name,
        roomId: invitation.room.id,
        memberId: user.id,
      });

      toast.success(`You joined "${invitation.room.name}"!`);

      // Refresh invitations list
      await loadInvitations();
      onInvitationUpdate?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to accept invitation");
    } finally {
      setProcessingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(invitation.id);
        return newSet;
      });
    }
  };

  const handleDeclineInvitation = async (invitation: InvitationWithDetails) => {
    if (!user) return;

    try {
      setProcessingIds((prev) => new Set(prev).add(invitation.id));

      await invitationService.declineInvitation(invitation.id, user.id);

      toast.success("Invitation declined");

      // Refresh invitations list
      await loadInvitations();
      onInvitationUpdate?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to decline invitation");
    } finally {
      setProcessingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(invitation.id);
        return newSet;
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses =
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";

    switch (status) {
      case "pending":
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case "accepted":
        return `${baseClasses} bg-green-100 text-green-800`;
      case "declined":
        return `${baseClasses} bg-red-100 text-red-800`;
      case "expired":
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending";
      case "accepted":
        return "Accepted";
      case "declined":
        return "Declined";
      case "expired":
        return "Expired";
      default:
        return status;
    }
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (invitations.length === 0) {
    return (
      <div className="text-center py-8">
        <svg
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">
          No invitations
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have any room invitations at the moment.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {invitations.map((invitation) => {
        const isProcessing = processingIds.has(invitation.id);
        const expired = isExpired(invitation.expires_at);
        const canRespond = invitation.status === "pending" && !expired;

        return (
          <div key={invitation.id} className="card p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="text-sm font-medium text-gray-900">
                    {invitation.room.name}
                  </h4>
                  <span
                    className={getStatusBadge(
                      expired ? "expired" : invitation.status
                    )}
                  >
                    {getStatusText(expired ? "expired" : invitation.status)}
                  </span>
                  {invitation.room.is_private && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      <svg
                        className="w-3 h-3 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2-2v6a2 2 0 002 2z"
                        />
                      </svg>
                      Private
                    </span>
                  )}
                </div>

                <p className="text-sm text-gray-600 mb-2">
                  Invited by{" "}
                  <span className="font-medium">
                    {invitation.invited_by_profile.full_name ||
                      invitation.invited_by_profile.email}
                  </span>
                </p>

                {invitation.message && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <p className="text-sm text-gray-700">
                      "{invitation.message}"
                    </p>
                  </div>
                )}

                <div className="flex items-center text-xs text-gray-500 space-x-4">
                  <span>Invited {formatDateTime(invitation.created_at)}</span>
                  <span>Expires {formatDateTime(invitation.expires_at)}</span>
                </div>
              </div>

              {canRespond && (
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleDeclineInvitation(invitation)}
                    disabled={isProcessing}
                    className="btn-secondary px-3 py-1 text-sm disabled:opacity-50"
                  >
                    {isProcessing ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                    ) : (
                      "Decline"
                    )}
                  </button>
                  <button
                    onClick={() => handleAcceptInvitation(invitation)}
                    disabled={isProcessing}
                    className="btn-primary px-3 py-1 text-sm disabled:opacity-50"
                  >
                    {isProcessing ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                    ) : (
                      "Accept"
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
