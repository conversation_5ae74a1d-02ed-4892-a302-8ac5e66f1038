import { useState } from "react";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason?: string) => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: "danger" | "warning" | "info";
  requireReason?: boolean;
  loading?: boolean;
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "info",
  requireReason = false,
  loading = false,
}: ConfirmationDialogProps) {
  const [reason, setReason] = useState("");

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (requireReason && !reason.trim()) return;
    onConfirm(reason.trim() || undefined);
  };

  const handleClose = () => {
    setReason("");
    onClose();
  };

  const getTypeStyles = () => {
    switch (type) {
      case "danger":
        return {
          icon: (
            <svg
              className="w-6 h-6 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          ),
          iconBg: "bg-red-100 dark:bg-red-900/20",
          confirmButton: "bg-red-600 hover:bg-red-700 focus:ring-red-500",
        };
      case "warning":
        return {
          icon: (
            <svg
              className="w-6 h-6 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          ),
          iconBg: "bg-yellow-100 dark:bg-yellow-900/20",
          confirmButton:
            "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",
        };
      default:
        return {
          icon: (
            <svg
              className="w-6 h-6 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          ),
          iconBg: "bg-blue-100 dark:bg-blue-900/20",
          confirmButton: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={handleClose}
        />

        {/* Dialog */}
        <div className="relative bg-white dark:bg-dark-800 rounded-xl shadow-xl max-w-md w-full transition-theme duration-theme">
          <div className="p-6">
            {/* Icon and Title */}
            <div className="flex items-center space-x-4 mb-4">
              <div
                className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${styles.iconBg}`}
              >
                {styles.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {title}
                </h3>
              </div>
            </div>

            {/* Message */}
            <div className="mb-6">
              <p className="text-gray-600 dark:text-gray-400">{message}</p>
            </div>

            {/* Reason Input */}
            {requireReason && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Reason {requireReason ? "(required)" : "(optional)"}
                </label>
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Please provide a reason for this action..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-theme duration-theme resize-none"
                />
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3 justify-end">
              <button
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-dark-700 hover:bg-gray-200 dark:hover:bg-dark-600 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {cancelText}
              </button>
              <button
                onClick={handleConfirm}
                disabled={loading || (requireReason && !reason.trim())}
                className={`px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 ${styles.confirmButton}`}
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  confirmText
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Bulk Actions Confirmation Dialog
interface BulkConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  action: string;
  selectedCount: number;
  loading?: boolean;
}

export function BulkConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  action,
  selectedCount,
  loading = false,
}: BulkConfirmationDialogProps) {
  if (!isOpen) return null;

  const getActionDetails = () => {
    switch (action) {
      case "delete":
        return {
          title: "Delete Users",
          message: `Are you sure you want to delete ${selectedCount} user${
            selectedCount > 1 ? "s" : ""
          }? This action cannot be undone and will permanently remove all user data including documents and room memberships.`,
          confirmText: "Delete Users",
          type: "danger" as const,
        };
      case "activate":
        return {
          title: "Activate Users",
          message: `Are you sure you want to activate ${selectedCount} user${
            selectedCount > 1 ? "s" : ""
          }? This will restore access to their accounts.`,
          confirmText: "Activate Users",
          type: "info" as const,
        };
      case "deactivate":
        return {
          title: "Deactivate Users",
          message: `Are you sure you want to deactivate ${selectedCount} user${
            selectedCount > 1 ? "s" : ""
          }? This will prevent them from accessing their accounts.`,
          confirmText: "Deactivate Users",
          type: "warning" as const,
        };
      default:
        return {
          title: "Confirm Action",
          message: `Are you sure you want to perform this action on ${selectedCount} user${
            selectedCount > 1 ? "s" : ""
          }?`,
          confirmText: "Confirm",
          type: "info" as const,
        };
    }
  };

  const details = getActionDetails();

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleConfirm}
      title={details.title}
      message={details.message}
      confirmText={details.confirmText}
      type={details.type}
      loading={loading}
    />
  );
}
