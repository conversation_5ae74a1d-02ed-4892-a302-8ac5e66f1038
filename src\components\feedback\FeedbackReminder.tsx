import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useAuth } from "../../contexts/AuthContext";
import { FeedbackModal } from "./FeedbackModal";

interface FeedbackReminderProps {
  onDismiss?: () => void;
}

const REMINDER_STORAGE_KEY = "brimbag-feedback-reminder";
const DONT_REMIND_STORAGE_KEY = "brimbag-feedback-dont-remind";
const REMINDER_INTERVAL_DAYS = 5;

interface ReminderData {
  lastShown: string;
  dismissCount: number;
  lastDismissed: string;
}

export function FeedbackReminder({ onDismiss }: FeedbackReminderProps) {
  const { user } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  useEffect(() => {
    if (!user) return;

    const checkShouldShowReminder = () => {
      // Check if user has opted out of reminders
      const dontRemind = localStorage.getItem(DONT_REMIND_STORAGE_KEY);
      if (dontRemind === "true") {
        return false;
      }

      // Get user registration date
      const userCreatedAt = new Date(user.created_at);
      const now = new Date();
      const daysSinceRegistration = Math.floor(
        (now.getTime() - userCreatedAt.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Don't show reminder immediately after registration
      if (daysSinceRegistration < 1) {
        return false;
      }

      // Get reminder data from localStorage
      const reminderDataStr = localStorage.getItem(REMINDER_STORAGE_KEY);
      let reminderData: ReminderData;

      if (reminderDataStr) {
        try {
          reminderData = JSON.parse(reminderDataStr);
        } catch {
          // If parsing fails, create new data
          reminderData = {
            lastShown: "",
            dismissCount: 0,
            lastDismissed: "",
          };
        }
      } else {
        // First time, create new data
        reminderData = {
          lastShown: "",
          dismissCount: 0,
          lastDismissed: "",
        };
      }

      // Check if enough time has passed since last reminder
      if (reminderData.lastShown) {
        const lastShown = new Date(reminderData.lastShown);
        const daysSinceLastShown = Math.floor(
          (now.getTime() - lastShown.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSinceLastShown < REMINDER_INTERVAL_DAYS) {
          return false;
        }
      }

      // Check if enough time has passed since last dismissal
      if (reminderData.lastDismissed) {
        const lastDismissed = new Date(reminderData.lastDismissed);
        const daysSinceLastDismissed = Math.floor(
          (now.getTime() - lastDismissed.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSinceLastDismissed < REMINDER_INTERVAL_DAYS) {
          return false;
        }
      }

      return true;
    };

    if (checkShouldShowReminder()) {
      setIsVisible(true);

      // Update last shown timestamp
      const reminderDataStr = localStorage.getItem(REMINDER_STORAGE_KEY);
      let reminderData: ReminderData;

      if (reminderDataStr) {
        try {
          reminderData = JSON.parse(reminderDataStr);
        } catch {
          reminderData = {
            lastShown: "",
            dismissCount: 0,
            lastDismissed: "",
          };
        }
      } else {
        reminderData = {
          lastShown: "",
          dismissCount: 0,
          lastDismissed: "",
        };
      }

      reminderData.lastShown = new Date().toISOString();
      localStorage.setItem(REMINDER_STORAGE_KEY, JSON.stringify(reminderData));
    }
  }, [user]);

  const handleDismiss = () => {
    setIsVisible(false);

    // Update dismissal data
    const reminderDataStr = localStorage.getItem(REMINDER_STORAGE_KEY);
    let reminderData: ReminderData;

    if (reminderDataStr) {
      try {
        reminderData = JSON.parse(reminderDataStr);
      } catch {
        reminderData = {
          lastShown: "",
          dismissCount: 0,
          lastDismissed: "",
        };
      }
    } else {
      reminderData = {
        lastShown: "",
        dismissCount: 0,
        lastDismissed: "",
      };
    }

    reminderData.dismissCount += 1;
    reminderData.lastDismissed = new Date().toISOString();
    localStorage.setItem(REMINDER_STORAGE_KEY, JSON.stringify(reminderData));

    onDismiss?.();
  };

  const handleDontRemindAgain = () => {
    localStorage.setItem(DONT_REMIND_STORAGE_KEY, "true");
    setIsVisible(false);
    onDismiss?.();
  };

  const handleGiveFeedback = () => {
    setShowFeedbackModal(true);
    setIsVisible(false);
  };

  if (!isVisible || !user) {
    return null;
  }

  return (
    <>
      {/* Reminder Banner */}
      <div className="fixed top-4 right-4 z-40 max-w-sm w-full">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-xl shadow-xl transition-theme duration-theme backdrop-blur-sm">
          <div className="p-5">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-lg">💬</span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                  💡 Share Your Feedback
                </h4>
                <p className="mt-xs text-gray-700 dark:text-gray-300">
                  Help us make BrimBag even better! Your insights drive our
                  improvements.
                </p>
                <div className="mt-4 flex items-center space-x-2">
                  <button
                    onClick={handleGiveFeedback}
                    className="px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.02]"
                  >
                    ✨ Give Feedback
                  </button>
                  <button
                    onClick={handleDismiss}
                    className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Later
                  </button>
                </div>
                <button
                  onClick={handleDontRemindAgain}
                  className="mt-2 text-xs text-gray-500 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  Don't show again
                </button>
              </div>
              <button
                onClick={handleDismiss}
                className="flex-shrink-0 p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-white/50 dark:hover:bg-dark-700/50 transition-colors"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <FeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          userId={user.id}
          onFeedbackSubmitted={() => {
            // Mark as completed feedback to reduce future reminders
            const reminderDataStr = localStorage.getItem(REMINDER_STORAGE_KEY);
            let reminderData: ReminderData;

            if (reminderDataStr) {
              try {
                reminderData = JSON.parse(reminderDataStr);
              } catch {
                reminderData = {
                  lastShown: "",
                  dismissCount: 0,
                  lastDismissed: "",
                };
              }
            } else {
              reminderData = {
                lastShown: "",
                dismissCount: 0,
                lastDismissed: "",
              };
            }

            // Reset dismiss count since user provided feedback
            reminderData.dismissCount = 0;
            reminderData.lastDismissed = new Date().toISOString();
            localStorage.setItem(
              REMINDER_STORAGE_KEY,
              JSON.stringify(reminderData)
            );
          }}
        />
      )}
    </>
  );
}

// Hook to manage feedback reminder state
export function useFeedbackReminder() {
  const [showReminder, setShowReminder] = useState(false);

  const dismissReminder = () => {
    setShowReminder(false);
  };

  return {
    showReminder,
    dismissReminder,
    FeedbackReminderComponent: () => (
      <FeedbackReminder onDismiss={dismissReminder} />
    ),
  };
}
