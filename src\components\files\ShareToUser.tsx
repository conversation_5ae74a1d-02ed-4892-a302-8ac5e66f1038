import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { fileService } from "../../lib/fileService";
import { userService } from "../../lib/userService";
import type { UserSearchResult } from "../../lib/userService";
import { UserSearch } from "../shared/UserSearch";
import { useAuth } from "../../contexts/AuthContext";

const _shareSchema = z.object({
  // No permission or expiration needed for permanent sharing
});

type ShareFormData = z.infer<typeof _shareSchema>;

interface ShareToUserProps {
  document: DocumentFile;
  onClose: () => void;
  onShareComplete?: () => void;
}

export function ShareToUser({
  document,
  onClose,
  onShareComplete,
}: ShareToUserProps) {
  const { user } = useAuth();
  const [selectedUsers, setSelectedUsers] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { handleSubmit } = useForm<ShareFormData>();

  const handleUserSelect = (selectedUser: UserSearchResult) => {
    if (!selectedUsers.find((u) => u.id === selectedUser.id)) {
      setSelectedUsers((prev) => [...prev, selectedUser]);
    }
  };

  const removeUser = (userId: string) => {
    setSelectedUsers((prev) => prev.filter((u) => u.id !== userId));
  };

  const onSubmit = async (_data: ShareFormData) => {
    if (!user) return;

    if (selectedUsers.length === 0) {
      toast.error("Please select at least one user to share with");
      return;
    }

    try {
      setIsLoading(true);

      // Share with each selected user (permanent sharing with full ownership)
      const sharePromises = selectedUsers.map((selectedUser) =>
        fileService.shareDocumentWithUser(
          document.id,
          selectedUser.id,
          user.id,
          "download", // Always download permission since they own the copy
          undefined // No expiration for permanent sharing
        )
      );

      await Promise.all(sharePromises);

      const userNames = selectedUsers.map((u) =>
        userService.getUserDisplayName(u)
      );
      const message =
        selectedUsers.length === 1
          ? `Document permanently shared with ${userNames[0]}! They now own a copy.`
          : `Document permanently shared with ${selectedUsers.length} users! They now own copies.`;

      toast.success(message);
      onShareComplete?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to share document");
    } finally {
      setIsLoading(false);
    }
  };

  const getUserInitials = (user: UserSearchResult): string => {
    return userService.getUserInitials(user);
  };

  const getUserDisplayName = (user: UserSearchResult): string => {
    return userService.getUserDisplayName(user);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Share Document (Permanent Copy)
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Document info */}
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-1">{document.title}</h3>
            <p className="text-sm text-gray-600">
              {document.file_type.toUpperCase()} •{" "}
              {Math.round(document.file_size / 1024)} KB
            </p>
          </div>

          {/* Permanent sharing notice */}
          {/* <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <svg
                className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">
                  Permanent File Sharing
                </h4>
                <p className="text-sm text-blue-700">
                  This document will be permanently copied to the recipient's
                  account. They will have full ownership with complete control
                  over their copy, including the ability to edit, move, delete,
                  and share it with others. The copy will count toward their
                  storage quota and remain accessible even if you delete your
                  original.
                </p>
              </div>
            </div>
          </div> */}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* User Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search and select users
              </label>
              <UserSearch
                onUserSelect={handleUserSelect}
                selectedUsers={selectedUsers}
                placeholder="Search users by name or email..."
                excludeUserIds={user ? [user.id] : []}
              />
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected users ({selectedUsers.length})
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {selectedUsers.map((selectedUser) => (
                    <div
                      key={selectedUser.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium text-sm">
                            {getUserInitials(selectedUser)}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {getUserDisplayName(selectedUser)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {selectedUser.email}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeUser(selectedUser.id)}
                        className="text-red-500 hover:text-red-700 p-1"
                        title="Remove user"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Ownership Transfer Notice */}
            {/* <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg
                  className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-green-900 mb-1">
                    Full Ownership Transfer
                  </h4>
                  <p className="text-sm text-green-700">
                    Recipients will receive complete ownership of their copy
                    with full permissions to view, download, edit, move, delete,
                    and share the document. No permission restrictions or
                    expiration dates apply.
                  </p>
                </div>
              </div>
            </div> */}

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || selectedUsers.length === 0}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading
                  ? "Creating copies..."
                  : `Create permanent ${
                      selectedUsers.length > 1 ? "copies" : "copy"
                    } for ${selectedUsers.length} user${
                      selectedUsers.length !== 1 ? "s" : ""
                    }`}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
