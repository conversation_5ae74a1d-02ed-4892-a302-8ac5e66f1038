# New Sharing Approach - Lazy File Copying

## Problem Solved
The previous approach failed due to **Row Level Security (RLS) policies** that prevent one user from creating files in another user's storage space.

## New Solution: Lazy File Copying

### How It Works

1. **Immediate Sharing**:
   - Create document record for recipient immediately
   - Initially references the original file (no copy yet)
   - Create share record linking original and shared documents
   - User sees shared document in their list immediately

2. **Lazy File Copying**:
   - File is copied when recipient first accesses it
   - Uses `ensureSharedFileExists()` function
   - Recipient's own permissions used for file operations
   - Avoids RLS conflicts

3. **Benefits**:
   - ✅ No RLS policy conflicts
   - ✅ Immediate sharing (no waiting for file copy)
   - ✅ Files copied only when needed
   - ✅ Works with existing storage permissions

### Implementation Details

#### 1. `createSharedDocumentRecord()`
- Creates document record for recipient
- Initially uses original file path
- No file copying at this stage

#### 2. `ensureSharedFileExists()`
- Called when recipient accesses shared document
- Checks if file needs copying
- Downloads original and uploads to recipient's storage
- Updates document record with new file path

#### 3. Storage Independence
- After first access, files are completely independent
- Recipient owns their copy
- Original can be deleted without affecting shared copy

### Usage Flow

1. **User A shares document with User B**:
   ```
   shareDocumentWithUser() → createSharedDocumentRecord() → Share created
   ```

2. **User B sees shared document in their list**:
   ```
   Document appears immediately (references original file)
   ```

3. **User B first accesses the document**:
   ```
   ensureSharedFileExists() → Download + Upload → Update file path
   ```

4. **Subsequent accesses**:
   ```
   Use User B's own copy (independent of original)
   ```

### Integration Points

The `ensureSharedFileExists()` function should be called:
- Before document download
- Before document preview
- Before any file access operation

### Backward Compatibility

- ✅ Works with existing database schema
- ✅ Supports both old and new sharing records
- ✅ Graceful fallback if copying fails

### Error Handling

- If lazy copying fails, document still accessible via original file
- User sees document but may get access errors if original is deleted
- Can retry copying on subsequent access attempts

### Future Enhancements

1. **Background copying**: Copy files in background after sharing
2. **Progress indicators**: Show copy progress to users
3. **Batch copying**: Copy multiple shared files at once
4. **Storage optimization**: Deduplicate identical files

## Testing

Try sharing a document now - it should work without RLS errors!

The sharing process will:
1. ✅ Create share record immediately
2. ✅ Show document in recipient's list
3. 🔄 Copy file when first accessed (lazy)

## Monitoring

Watch for:
- Successful share creation (no RLS errors)
- Documents appearing in shared lists
- File copying on first access
- Independent file operations after copying
