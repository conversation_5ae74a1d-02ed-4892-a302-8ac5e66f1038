import { useState, useEffect } from "react";
import { fileService } from "../../lib/fileService";
import type { DocumentFile } from "../../lib/fileService";
import toast from "react-hot-toast";
import { formatFileSize, formatDateTime } from "../../lib/utils";
import { EnhancedPdfViewer } from "./EnhancedPdfViewer";
import { OfficeFileViewer } from "./OfficeFileViewer";
import { useAuth } from "../../contexts/AuthContext";

interface DocumentPreviewProps {
  document: DocumentFile;
  onClose: () => void;
  onDownload?: () => void;
}

export function DocumentPreview({
  document,
  onClose,
  onDownload,
}: DocumentPreviewProps) {
  const { user } = useAuth();
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [textContent, setTextContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [downloadProgress, setDownloadProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState(false);

  useEffect(() => {
    loadPreview();
  }, [document]);

  const loadPreview = async () => {
    try {
      setIsLoading(true);
      setError("");

      if (!user?.id) {
        setError("User not authenticated");
        return;
      }

      if (fileService.canPreview(document.file_type, document.mime_type)) {
        if (document.mime_type === "text/plain") {
          const content = await fileService.getTextContent(document);
          setTextContent(content);
        } else {
          const url = await fileService.getPreviewUrl(document, user.id);
          setPreviewUrl(url);
        }
      }
    } catch (error: any) {
      console.error("Preview load error:", error);
      setError(error.message || "Failed to load preview");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      setDownloadProgress(0);

      if (!user?.id) {
        toast.error("User not authenticated");
        return;
      }

      await fileService.downloadDocument(
        document,
        (progress) => {
          setDownloadProgress(progress);
        },
        user.id
      );

      toast.success("Download completed!");
      onDownload?.();
    } catch (error: any) {
      toast.error(error.message || "Download failed");
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const canPreview = fileService.canPreview(
    document.file_type,
    document.mime_type
  );
  const isImage = document.mime_type.startsWith("image/");
  const isPdf = document.mime_type === "application/pdf";
  const isText = document.mime_type === "text/plain";
  const isOfficeFile = [
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel",
  ].includes(document.mime_type);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-2 sm:p-4 z-50">
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-xl w-full h-full max-w-7xl max-h-[95vh] flex flex-col transition-theme duration-theme">
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-6 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <span className="text-2xl sm:text-3xl flex-shrink-0">
              {fileService.getFileIcon(document.file_type)}
            </span>
            <div className="min-w-0 flex-1">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-900 dark:text-gray-100 truncate transition-theme duration-theme">
                {document.title}
              </h3>
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                <span className="bg-gray-100 dark:bg-dark-700 px-2 py-1 rounded text-xs transition-theme duration-theme">
                  {document.file_type}
                </span>
                <span className="hidden sm:inline">
                  {formatFileSize(document.file_size)}
                </span>
                <span className="hidden md:inline">
                  {formatDateTime(document.created_at)}
                </span>
                <span className="hidden lg:inline">
                  {document.download_count} downloads
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className="btn-primary flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2"
            >
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white"></div>
                  <span className="hidden sm:inline">
                    {Math.round(downloadProgress)}%
                  </span>
                </>
              ) : (
                <>
                  <svg
                    className="w-3 h-3 sm:w-4 sm:h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <span className="hidden sm:inline">Download</span>
                </>
              )}
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 p-1 transition-theme duration-theme"
            >
              <svg
                className="w-5 h-5 sm:w-6 sm:h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                  Loading preview...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-red-500 text-6xl mb-4">⚠️</div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
                  Preview Error
                </h4>
                <p className="text-gray-600 dark:text-gray-400 mb-4 transition-theme duration-theme">
                  {error}
                </p>
                <button onClick={handleDownload} className="btn-primary">
                  Download File Instead
                </button>
              </div>
            </div>
          ) : !canPreview && !isOfficeFile ? (
            <div className="flex items-center justify-center h-full p-4">
              <div className="text-center max-w-md mx-auto">
                <div className="text-gray-400 text-4xl sm:text-6xl mb-4">
                  {fileService.getFileIcon(document.file_type)}
                </div>
                <h4 className="text-base sm:text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
                  Preview Not Available
                </h4>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-4 transition-theme duration-theme">
                  {document.file_type === "Word Document" ||
                  document.file_type === "word document"
                    ? "Word documents cannot be previewed in the browser. Download the file to view it in Microsoft Word or a compatible application."
                    : document.file_type === "Presentation" ||
                      document.file_type === "presentation"
                    ? "PowerPoint presentations cannot be previewed in the browser. Download the file to view it in Microsoft PowerPoint or a compatible application."
                    : document.file_type === "Spreadsheet" ||
                      document.file_type === "spreadsheet"
                    ? "Excel spreadsheets cannot be previewed in the browser. Download the file to view it in Microsoft Excel or a compatible application."
                    : "This file type doesn't support preview. Download it to view the content in the appropriate application."}
                </p>
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 space-y-2 text-xs sm:text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span className="font-medium">File Type:</span>
                    <span>{document.file_type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Size:</span>
                    <span>{formatFileSize(document.file_size)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Uploaded:</span>
                    <span>{formatDateTime(document.created_at)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Downloads:</span>
                    <span>{document.download_count}</span>
                  </div>
                  {document.description && (
                    <div className="pt-2 border-t border-gray-200">
                      <span className="font-medium">Description:</span>
                      <p className="mt-1 text-gray-700">
                        {document.description}
                      </p>
                    </div>
                  )}
                </div>
                <button
                  onClick={handleDownload}
                  className="btn-primary w-full sm:w-auto"
                  disabled={isDownloading}
                >
                  {isDownloading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Downloading... {Math.round(downloadProgress)}%
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      Download File
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : isText ? (
            <div className="h-full overflow-auto p-3 sm:p-6">
              <pre className="whitespace-pre-wrap font-mono text-xs sm:text-sm bg-gray-50 dark:bg-dark-700 dark:text-gray-100 p-3 sm:p-4 rounded-lg border dark:border-dark-600 transition-theme duration-theme">
                {textContent}
              </pre>
            </div>
          ) : isImage ? (
            <div className="h-full overflow-auto p-3 sm:p-6 flex items-center justify-center">
              <img
                src={previewUrl}
                alt={document.title}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                onError={() => setError("Failed to load image")}
              />
            </div>
          ) : isPdf ? (
            <div className="h-full">
              <EnhancedPdfViewer
                fileUrl={previewUrl}
                fileName={document.title}
                onError={(error) => setError(error)}
              />
            </div>
          ) : isOfficeFile ? (
            <div className="h-full">
              <OfficeFileViewer
                fileUrl={previewUrl}
                fileName={document.title}
                fileType={document.file_type}
                mimeType={document.mime_type}
                onError={(error) => setError(error)}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-gray-400 text-6xl mb-4">📄</div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Preview Not Supported
                </h4>
                <p className="text-gray-600 mb-4">
                  This file type is not supported for preview.
                </p>
                <button onClick={handleDownload} className="btn-primary">
                  Download File
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Download Progress */}
        {isDownloading && (
          <div className="p-3 sm:p-4 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs sm:text-sm text-gray-600">
                Downloading...
              </span>
              <span className="text-xs sm:text-sm text-gray-600">
                {Math.round(downloadProgress)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${downloadProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
