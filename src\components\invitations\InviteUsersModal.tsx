import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import { UserSearch } from "../shared/UserSearch";
import { invitationService } from "../../lib/invitationService";
import { notificationService } from "../../lib/notificationService";
import type { UserSearchResult } from "../../lib/userService";
import type { RoomWithDetails } from "../../lib/roomService";
import { useAuth } from "../../contexts/AuthContext";

const inviteSchema = z.object({
  message: z.string().max(500, "Message too long").optional(),
});

type InviteFormData = z.infer<typeof inviteSchema>;

interface InviteUsersModalProps {
  room: RoomWithDetails;
  onClose: () => void;
  onInviteComplete?: () => void;
}

export function InviteUsersModal({
  room,
  onClose,
  onInviteComplete,
}: InviteUsersModalProps) {
  const { user } = useAuth();
  const [selectedUsers, setSelectedUsers] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<InviteFormData>({
    defaultValues: {
      message: "",
    },
  });

  const message = watch("message");

  const handleUserSelect = (selectedUser: UserSearchResult) => {
    if (!selectedUsers.find((u) => u.id === selectedUser.id)) {
      setSelectedUsers([...selectedUsers, selectedUser]);
    }
  };

  const removeUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter((u) => u.id !== userId));
  };

  const onSubmit = async (data: InviteFormData) => {
    if (!user || selectedUsers.length === 0) return;

    try {
      setIsLoading(true);

      // Send invitations to all selected users
      const invitationPromises = selectedUsers.map(async (selectedUser) => {
        // Send invitation
        const invitation = await invitationService.inviteUserToRoom({
          roomId: room.id,
          invitedUserId: selectedUser.id,
          invitedBy: user.id,
          message: data.message,
        });

        // Send notification
        await notificationService.notifyRoomInvitation({
          invitedUserId: selectedUser.id,
          inviterName: user.profile?.full_name || user.email || "Someone",
          roomName: room.name,
          roomId: room.id,
          invitationId: invitation.id,
        });

        return invitation;
      });

      await Promise.all(invitationPromises);

      toast.success(
        `Invitations sent to ${selectedUsers.length} user${
          selectedUsers.length > 1 ? "s" : ""
        }`
      );

      onInviteComplete?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to send invitations");
    } finally {
      setIsLoading(false);
    }
  };

  // Get existing room member IDs to exclude from search
  const excludeUserIds = [
    room.created_by,
    // Note: We'll exclude current members in the UserSearch component
    // since we don't have members data in this component
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Invite Users to {room.name}
            </h3>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* User Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search and select users to invite
              </label>
              <UserSearch
                onUserSelect={handleUserSelect}
                selectedUsers={selectedUsers}
                excludeUserIds={excludeUserIds}
                placeholder="Search users by name or email..."
                disabled={isLoading}
              />
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected users ({selectedUsers.length})
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {selectedUsers.map((selectedUser) => (
                    <div
                      key={selectedUser.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">
                            {(selectedUser.full_name ||
                              selectedUser.email)[0].toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {selectedUser.full_name || "No name"}
                          </p>
                          <p className="text-xs text-gray-500">
                            {selectedUser.email}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeUser(selectedUser.id)}
                        disabled={isLoading}
                        className="text-gray-400 hover:text-red-500 disabled:opacity-50"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Optional Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Optional message
              </label>
              <textarea
                {...register("message")}
                disabled={isLoading}
                placeholder="Add a personal message to your invitation..."
                rows={3}
                className="input-field resize-none"
              />
              {errors.message && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.message.message}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {message?.length || 0}/500 characters
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="btn-secondary flex-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || selectedUsers.length === 0}
                className="btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  `Send ${
                    selectedUsers.length > 0 ? `${selectedUsers.length} ` : ""
                  }Invitation${selectedUsers.length !== 1 ? "s" : ""}`
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
