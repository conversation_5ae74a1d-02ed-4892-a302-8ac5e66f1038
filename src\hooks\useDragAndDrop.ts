import { useState, useCallback } from "react";

export interface DragItem {
  type: "document" | "folder";
  id: string;
  data: any;
}

export interface DropTarget {
  type: "folder" | "root";
  id: string | null;
  data?: any;
}

export interface DragAndDropState {
  isDragging: boolean;
  dragItem: DragItem | null;
  dragOverTarget: DropTarget | null;
}

export function useDragAndDrop() {
  const [state, setState] = useState<DragAndDropState>({
    isDragging: false,
    dragItem: null,
    dragOverTarget: null,
  });

  const startDrag = useCallback((item: DragItem) => {
    setState(prev => ({
      ...prev,
      isDragging: true,
      dragItem: item,
    }));
  }, []);

  const endDrag = useCallback(() => {
    setState(prev => ({
      ...prev,
      isDragging: false,
      dragItem: null,
      dragOverTarget: null,
    }));
  }, []);

  const setDragOverTarget = useCallback((target: DropTarget | null) => {
    setState(prev => ({
      ...prev,
      dragOverTarget: target,
    }));
  }, []);

  const createDragHandlers = useCallback((item: DragItem) => ({
    draggable: true,
    onDragStart: (e: React.DragEvent) => {
      e.dataTransfer.setData("application/json", JSON.stringify(item));
      e.dataTransfer.effectAllowed = "move";
      startDrag(item);
    },
    onDragEnd: () => {
      endDrag();
    },
  }), [startDrag, endDrag]);

  const createDropHandlers = useCallback((
    target: DropTarget,
    onDrop: (dragItem: DragItem, dropTarget: DropTarget) => void
  ) => ({
    onDragOver: (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
      setDragOverTarget(target);
    },
    onDragEnter: (e: React.DragEvent) => {
      e.preventDefault();
      setDragOverTarget(target);
    },
    onDragLeave: (e: React.DragEvent) => {
      // Only clear drag over target if we're leaving the element entirely
      if (!e.currentTarget.contains(e.relatedTarget as Node)) {
        setDragOverTarget(null);
      }
    },
    onDrop: (e: React.DragEvent) => {
      e.preventDefault();
      try {
        const dragData = e.dataTransfer.getData("application/json");
        if (dragData) {
          const dragItem: DragItem = JSON.parse(dragData);
          onDrop(dragItem, target);
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
      setDragOverTarget(null);
    },
  }), [setDragOverTarget]);

  const isDragOverTarget = useCallback((target: DropTarget) => {
    return state.dragOverTarget?.type === target.type && 
           state.dragOverTarget?.id === target.id;
  }, [state.dragOverTarget]);

  const canDrop = useCallback((dragItem: DragItem, dropTarget: DropTarget) => {
    // Prevent dropping on self
    if (dragItem.type === "folder" && dropTarget.type === "folder" && dragItem.id === dropTarget.id) {
      return false;
    }

    // Documents can be dropped on folders or root
    if (dragItem.type === "document") {
      return dropTarget.type === "folder" || dropTarget.type === "root";
    }

    // Folders can be dropped on other folders or root (for moving)
    if (dragItem.type === "folder") {
      return dropTarget.type === "folder" || dropTarget.type === "root";
    }

    return false;
  }, []);

  return {
    state,
    createDragHandlers,
    createDropHandlers,
    isDragOverTarget,
    canDrop,
  };
}
