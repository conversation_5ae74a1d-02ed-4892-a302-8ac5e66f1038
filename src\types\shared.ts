// Shared type definitions used across BrimBag
import type { Database } from "../lib/supabase";

// Re-export database types for shared use
export type { Database } from "../lib/supabase";

// User and Profile types
export type UserRole = "student" | "lecturer" | "admin";
export type Profile = Database["public"]["Tables"]["profiles"]["Row"];
export type ProfileInsert = Database["public"]["Tables"]["profiles"]["Insert"];
export type ProfileUpdate = Database["public"]["Tables"]["profiles"]["Update"];

// Document types
export type Document = Database["public"]["Tables"]["documents"]["Row"];
export type DocumentInsert =
  Database["public"]["Tables"]["documents"]["Insert"];
export type DocumentUpdate =
  Database["public"]["Tables"]["documents"]["Update"];

// Room types
export type Room = Database["public"]["Tables"]["rooms"]["Row"];
export type RoomInsert = Database["public"]["Tables"]["rooms"]["Insert"];
export type RoomUpdate = Database["public"]["Tables"]["rooms"]["Update"];

export type RoomMember = Database["public"]["Tables"]["room_members"]["Row"];
export type RoomMemberInsert =
  Database["public"]["Tables"]["room_members"]["Insert"];

// Folder types
export type Folder = Database["public"]["Tables"]["folders"]["Row"];
export type FolderInsert = Database["public"]["Tables"]["folders"]["Insert"];
export type FolderUpdate = Database["public"]["Tables"]["folders"]["Update"];

// Document sharing types
export type DocumentShare =
  Database["public"]["Tables"]["document_shares"]["Row"];
export type DocumentShareInsert =
  Database["public"]["Tables"]["document_shares"]["Insert"];

// Room document types
export type RoomDocument =
  Database["public"]["Tables"]["room_documents"]["Row"];
export type RoomDocumentInsert =
  Database["public"]["Tables"]["room_documents"]["Insert"];

// Notification types
export type Notification = Database["public"]["Tables"]["notifications"]["Row"];
export type NotificationInsert =
  Database["public"]["Tables"]["notifications"]["Insert"];

// Invitation types
export type RoomInvitation =
  Database["public"]["Tables"]["room_invitations"]["Row"];
export type RoomInvitationInsert =
  Database["public"]["Tables"]["room_invitations"]["Insert"];

export type RoomInvitationLink =
  Database["public"]["Tables"]["room_invitation_links"]["Row"];
export type RoomInvitationLinkInsert =
  Database["public"]["Tables"]["room_invitation_links"]["Insert"];

// Common interfaces used across the app
export interface FileUploadProgress {
  file: File;
  progress: number;
  status: "pending" | "uploading" | "completed" | "error";
  error?: string;
}

export interface DashboardStats {
  totalDocuments: number;
  sharedFiles: number;
  activeRooms: number;
  storageUsed: number;
  recentFiles: Array<{
    id: string;
    title: string;
    file_type: string;
    file_size: number;
    created_at: string;
  }>;
}

export interface UserStatistics {
  documentCount: number;
  storageUsed: number;
  roomsJoined: number;
  roomsCreated: number;
  sharedDocuments: number;
}

export interface RoomWithDetails extends Room {
  member_count: number;
  document_count: number;
  is_member: boolean;
  user_role: "admin" | "member";
}

export interface RoomMemberWithProfile extends RoomMember {
  profiles: {
    full_name: string | null;
    email: string;
    role: UserRole;
  } | null;
}

export interface DocumentWithFolder extends Document {
  folder?: {
    id: string;
    name: string;
    path: string;
  } | null;
}

export interface FolderWithStats extends Folder {
  document_count: number;
  subfolder_count: number;
  total_size: number;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedApiResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Form validation types
export interface FormError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormError[];
}

// File type definitions
export type SupportedFileType =
  | "application/pdf"
  | "application/msword"
  | "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  | "application/vnd.ms-powerpoint"
  | "application/vnd.openxmlformats-officedocument.presentationml.presentation"
  | "application/vnd.ms-excel"
  | "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  | "text/plain"
  | "image/jpeg"
  | "image/png"
  | "image/gif"
  | "image/webp";

export interface FileTypeInfo {
  extension: string;
  mimeType: SupportedFileType;
  category: "document" | "presentation" | "spreadsheet" | "image" | "text";
  icon: string;
  previewable: boolean;
}

// Theme types
export type Theme = "light" | "dark";

// Navigation types
export interface NavigationItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  current?: boolean;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  file_type?: string;
  date_from?: string;
  date_to?: string;
  folder_id?: string;
  sort_by?: "name" | "date" | "size" | "type";
  sort_order?: "asc" | "desc";
}

// Storage types
export interface StorageInfo {
  used: number;
  capacity: number;
  percentage: number;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Loading state types
export type LoadingState = "idle" | "loading" | "success" | "error";

// Modal types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// Toast notification types
export type ToastType = "success" | "error" | "warning" | "info";

export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}
