-- Migration to implement permanent file sharing
-- This migration updates the document_shares table to support permanent sharing

-- Step 1: Drop existing RLS policies that reference the old column
DROP POLICY IF EXISTS "Users can view shared documents" ON documents;
DROP POLICY IF EXISTS "Users can view shares involving them" ON document_shares;

-- Step 2: Add new columns to document_shares table
ALTER TABLE document_shares 
ADD COLUMN IF NOT EXISTS original_document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS shared_document_id UUID REFERENCES documents(id) ON DELETE CASCADE;

-- Step 3: Migrate existing data (if any exists)
-- For existing shares, set original_document_id to the current document_id
-- and shared_document_id to NULL (will be populated when shares are recreated)
UPDATE document_shares 
SET original_document_id = document_id 
WHERE original_document_id IS NULL;

-- Step 4: Drop the old document_id column and unique constraint
ALTER TABLE document_shares DROP CONSTRAINT IF EXISTS document_shares_document_id_shared_with_key;
ALTER TABLE document_shares DROP COLUMN IF EXISTS document_id;

-- Step 5: Add new unique constraint
ALTER TABLE document_shares ADD CONSTRAINT document_shares_original_document_id_shared_with_key 
UNIQUE(original_document_id, shared_with);

-- Step 6: Recreate RLS policies with new column names
CREATE POLICY "Users can view shared documents" ON documents FOR SELECT USING (
  id IN (SELECT shared_document_id FROM document_shares WHERE shared_with = auth.uid())
  OR id IN (
    SELECT rd.document_id FROM room_documents rd
    JOIN room_members rm ON rd.room_id = rm.room_id
    WHERE rm.user_id = auth.uid()
  )
);

CREATE POLICY "Users can view shares involving them" ON document_shares FOR SELECT USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

CREATE POLICY "Users can create shares for their documents" ON document_shares FOR INSERT WITH CHECK (
  original_document_id IN (SELECT id FROM documents WHERE user_id = auth.uid())
);

CREATE POLICY "Users can delete their shares" ON document_shares FOR DELETE USING (
  shared_by = auth.uid() OR shared_with = auth.uid()
);

-- Step 7: Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_document_shares_original_document_id ON document_shares(original_document_id);
CREATE INDEX IF NOT EXISTS idx_document_shares_shared_document_id ON document_shares(shared_document_id);
CREATE INDEX IF NOT EXISTS idx_document_shares_shared_with ON document_shares(shared_with);
CREATE INDEX IF NOT EXISTS idx_document_shares_shared_by ON document_shares(shared_by);
