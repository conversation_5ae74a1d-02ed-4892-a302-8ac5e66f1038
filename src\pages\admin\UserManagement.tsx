import { useState, useEffect } from "react";
import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";
import { adminService } from "../../lib/admin/adminService";
import { formatFileSize } from "../../lib/admin/adminUtils";
import { UserDetailsModal } from "../../components/admin/UserDetailsModal";
import {
  ConfirmationDialog,
  BulkConfirmationDialog,
} from "../../components/admin/ConfirmationDialog";
import { AlertSystem, useAlerts } from "../../components/admin/AlertSystem";
import type {
  AdminUser,
  UserFilters,
  PaginatedResponse,
  BulkOperationResult,
} from "../../types/admin";

export function UserManagement() {
  usePageTitle("User Management - BrimBag");

  // Alert system
  const { alerts, removeAlert, showSuccess, showError, showWarning } =
    useAlerts();

  // State management
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());

  const [showUserDetails, setShowUserDetails] = useState<AdminUser | null>(
    null
  );
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [filters, setFilters] = useState<UserFilters>({});
  const [searchTerm, setSearchTerm] = useState("");

  // Individual user actions
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    type: "activate" | "deactivate" | "delete";
    user: AdminUser;
    callback: (reason?: string) => void;
  } | null>(null);
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null);

  // Bulk operations
  const [bulkOperation, setBulkOperation] = useState<
    "delete" | "activate" | "deactivate" | null
  >(null);
  const [bulkLoading, setBulkLoading] = useState(false);
  const [showBulkConfirm, setShowBulkConfirm] = useState(false);

  // Load users
  const loadUsers = async (
    page: number = 1,
    newFilters: UserFilters = filters
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response: PaginatedResponse<AdminUser> =
        await adminService.getAllUsers(newFilters, page, 20);

      setUsers(response.data);
      setCurrentPage(response.page);
      setTotalPages(response.total_pages);
      setTotalUsers(response.total);
    } catch (err: any) {
      setError(err.message || "Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadUsers();
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Check if click is outside bulk actions dropdown
      if (showBulkActions) {
        const bulkDropdown = target.closest("[data-bulk-dropdown]");
        if (!bulkDropdown) {
          setShowBulkActions(false);
        }
      }

      // Check if click is outside action menu dropdown
      if (openActionMenu) {
        const actionDropdown = target.closest("[data-action-dropdown]");
        if (!actionDropdown) {
          setOpenActionMenu(null);
        }
      }
    };

    if (showBulkActions || openActionMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showBulkActions, openActionMenu]);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    const newFilters = { ...filters, search: term || undefined };
    setFilters(newFilters);
    setCurrentPage(1);
    loadUsers(1, newFilters);
  };

  // Handle filter change
  // const handleFilterChange = (key: keyof UserFilters, value: any) => {
  //   const newFilters = { ...filters, [key]: value || undefined };
  //   setFilters(newFilters);
  //   setCurrentPage(1);
  //   loadUsers(1, newFilters);
  // };

  // Handle user selection
  const handleUserSelect = (userId: string, selected: boolean) => {
    const newSelected = new Set(selectedUsers);
    if (selected) {
      newSelected.add(userId);
    } else {
      newSelected.delete(userId);
    }
    setSelectedUsers(newSelected);
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUsers(new Set(users.map((user) => user.id)));
    } else {
      setSelectedUsers(new Set());
    }
  };

  // Individual user actions
  const handleUserAction = (
    type: "activate" | "deactivate" | "delete",
    user: AdminUser
  ) => {
    const callback = async (reason?: string) => {
      setActionLoading(user.id);
      try {
        switch (type) {
          case "activate":
            await adminService.activateUser(user.id);
            showSuccess(
              "User Activated",
              `${user.full_name || user.email} has been activated successfully.`
            );
            break;
          case "deactivate":
            await adminService.deactivateUser(user.id, reason);
            showSuccess(
              "User Deactivated",
              `${
                user.full_name || user.email
              } has been deactivated successfully.`
            );
            break;
          case "delete":
            await adminService.deleteUser(user.id);
            showSuccess(
              "User Deleted",
              `${user.full_name || user.email} has been deleted successfully.`
            );
            break;
        }
        await loadUsers(currentPage);
      } catch (err: any) {
        showError("Action Failed", err.message || `Failed to ${type} user`);
      } finally {
        setActionLoading(null);
        setShowConfirmDialog(false);
        setConfirmAction(null);
      }
    };

    setConfirmAction({ type, user, callback });
    setShowConfirmDialog(true);
  };

  // Handle bulk operations
  const handleBulkOperation = async () => {
    if (selectedUsers.size === 0 || !bulkOperation) return;

    setBulkLoading(true);
    try {
      let result: BulkOperationResult;

      switch (bulkOperation) {
        case "delete":
          result = await adminService.bulkDeleteUsers(
            Array.from(selectedUsers)
          );
          break;
        case "activate":
          // Note: We'll need to implement bulk activate in adminService
          showWarning(
            "Not Implemented",
            "Bulk activate functionality will be implemented soon."
          );
          return;
        case "deactivate":
          // Note: We'll need to implement bulk deactivate in adminService
          showWarning(
            "Not Implemented",
            "Bulk deactivate functionality will be implemented soon."
          );
          return;
        default:
          console.error("Unknown bulk operation:", bulkOperation);
          return;
      }

      // Show results and reload
      if (result.success > 0) {
        showSuccess(
          "Bulk Operation Complete",
          `Successfully processed ${result.success} user${
            result.success > 1 ? "s" : ""
          }.`
        );
      }

      if (result.failed > 0) {
        showError(
          "Partial Failure",
          `${result.failed} operation${
            result.failed > 1 ? "s" : ""
          } failed. ${result.errors.slice(0, 3).join(", ")}${
            result.errors.length > 3 ? "..." : ""
          }`
        );
      }

      await loadUsers(currentPage);
      setSelectedUsers(new Set());
      setShowBulkActions(false);
      setBulkOperation(null);
      setShowBulkConfirm(false);
    } catch (err: any) {
      showError(
        "Bulk Operation Failed",
        err.message || "Failed to perform bulk operation"
      );
    } finally {
      setBulkLoading(false);
    }
  };

  // Handle bulk action selection
  const handleBulkActionSelect = (
    action: "delete" | "activate" | "deactivate"
  ) => {
    setBulkOperation(action);
    setShowBulkActions(false);
    setShowBulkConfirm(true);
  };

  return (
    <AdminLayout>
      {/* Alert System */}
      <AlertSystem alerts={alerts} onRemoveAlert={removeAlert} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                User Management
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                Manage user accounts and permissions across the platform.
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex items-center space-x-3">
              {selectedUsers.size > 0 && (
                <div className="relative" data-bulk-dropdown>
                  <button
                    onClick={() => setShowBulkActions(!showBulkActions)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                      />
                    </svg>
                    Bulk Actions ({selectedUsers.size})
                    <svg
                      className="w-4 h-4 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  {/* Bulk Actions Dropdown */}
                  {showBulkActions && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-10">
                      <div className="py-1">
                        <button
                          onClick={() => handleBulkActionSelect("activate")}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200"
                        >
                          <svg
                            className="w-4 h-4 inline mr-2 text-green-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          Activate Users
                        </button>
                        <button
                          onClick={() => handleBulkActionSelect("deactivate")}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200"
                        >
                          <svg
                            className="w-4 h-4 inline mr-2 text-yellow-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          Deactivate Users
                        </button>
                        <hr className="my-1 border-gray-200 dark:border-dark-600" />
                        <button
                          onClick={() => handleBulkActionSelect("delete")}
                          className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                        >
                          <svg
                            className="w-4 h-4 inline mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                          Delete Users
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <button
                onClick={() => loadUsers(currentPage)}
                className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg transition-colors duration-200"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-6 mb-6 transition-theme duration-theme">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Users
            </label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search by name or email..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-theme duration-theme"
              />
              <svg
                className="absolute left-3 top-2.5 w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>

            {/* Clear Search */}
            {searchTerm && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-dark-600">
                <button
                  onClick={() => {
                    setFilters({});
                    setSearchTerm("");
                    loadUsers(1, {});
                  }}
                  className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                >
                  Clear search
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex">
              <svg
                className="w-5 h-5 text-red-400 mr-3 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error}
                </p>
                <button
                  onClick={() => setError(null)}
                  className="text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 font-medium mt-2 transition-colors duration-200"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading users...</p>
          </div>
        )}

        {/* Users Table */}
        {!loading && users.length > 0 && (
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 overflow-hidden transition-theme duration-theme">
            {/* Table Header */}
            <div className="px-6 py-4 border-b border-gray-200 dark:border-dark-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Users ({totalUsers})
                </h3>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={
                      selectedUsers.size === users.length && users.length > 0
                    }
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label className="text-sm text-gray-600 dark:text-gray-400">
                    Select All
                  </label>
                </div>
              </div>
            </div>

            {/* Table Content */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-dark-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Select
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Storage Used
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Documents
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Joined
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-dark-700">
                  {users.map((user) => (
                    <tr
                      key={user.id}
                      className="hover:bg-gray-50 dark:hover:bg-dark-700/50 transition-colors duration-200"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedUsers.has(user.id)}
                          onChange={(e) =>
                            handleUserSelect(user.id, e.target.checked)
                          }
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-500 flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {user.full_name?.charAt(0) ||
                                  user.email.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {user.full_name || "No name"}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.active
                                ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                                : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                            }`}
                          >
                            {user.active ? "Active" : "Inactive"}
                          </span>
                          {!user.active && user.deactivated_at && (
                            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {new Date(
                                user.deactivated_at
                              ).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatFileSize(user.user_stats.storage_used)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {user.user_stats.document_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(user.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="relative" data-action-dropdown>
                          <button
                            onClick={() =>
                              setOpenActionMenu(
                                openActionMenu === user.id ? null : user.id
                              )
                            }
                            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200"
                            title="User actions"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                              />
                            </svg>
                          </button>

                          {/* Action Menu Dropdown */}
                          {openActionMenu === user.id && (
                            <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-20">
                              <div className="py-1">
                                <button
                                  onClick={() => {
                                    setShowUserDetails(user);
                                    setOpenActionMenu(null);
                                  }}
                                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200 flex items-center"
                                >
                                  <svg
                                    className="w-4 h-4 mr-3 text-blue-500"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                  </svg>
                                  View Details
                                </button>

                                {/* Show Activate only if user is inactive */}
                                {!user.active && (
                                  <button
                                    onClick={() => {
                                      handleUserAction("activate", user);
                                      setOpenActionMenu(null);
                                    }}
                                    disabled={actionLoading === user.id}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200 disabled:opacity-50 flex items-center"
                                  >
                                    {actionLoading === user.id ? (
                                      <div className="animate-spin w-4 h-4 mr-3 border-2 border-green-500 border-t-transparent rounded-full"></div>
                                    ) : (
                                      <svg
                                        className="w-4 h-4 mr-3 text-green-500"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                      </svg>
                                    )}
                                    Activate User
                                  </button>
                                )}

                                {/* Show Deactivate only if user is active */}
                                {user.active && (
                                  <button
                                    onClick={() => {
                                      handleUserAction("deactivate", user);
                                      setOpenActionMenu(null);
                                    }}
                                    disabled={actionLoading === user.id}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors duration-200 disabled:opacity-50 flex items-center"
                                  >
                                    {actionLoading === user.id ? (
                                      <div className="animate-spin w-4 h-4 mr-3 border-2 border-yellow-500 border-t-transparent rounded-full"></div>
                                    ) : (
                                      <svg
                                        className="w-4 h-4 mr-3 text-yellow-500"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                      </svg>
                                    )}
                                    Deactivate User
                                  </button>
                                )}

                                <hr className="my-1 border-gray-200 dark:border-dark-600" />

                                <button
                                  onClick={() => {
                                    handleUserAction("delete", user);
                                    setOpenActionMenu(null);
                                  }}
                                  disabled={actionLoading === user.id}
                                  className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200 disabled:opacity-50 flex items-center"
                                >
                                  {actionLoading === user.id ? (
                                    <div className="animate-spin w-4 h-4 mr-3 border-2 border-red-500 border-t-transparent rounded-full"></div>
                                  ) : (
                                    <svg
                                      className="w-4 h-4 mr-3"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                      />
                                    </svg>
                                  )}
                                  Delete User
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-dark-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Showing page {currentPage} of {totalPages} ({totalUsers}{" "}
                    total users)
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => loadUsers(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {currentPage} / {totalPages}
                    </span>
                    <button
                      onClick={() => loadUsers(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && users.length === 0 && (
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
            <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No users found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {Object.keys(filters).length > 0 || searchTerm
                ? "No users match your current filters. Try adjusting your search criteria."
                : "No users have been registered yet."}
            </p>
            {(Object.keys(filters).length > 0 || searchTerm) && (
              <button
                onClick={() => {
                  setFilters({});
                  setSearchTerm("");
                  loadUsers(1, {});
                }}
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
              >
                Clear Filters
              </button>
            )}
          </div>
        )}

        {/* User Details Modal */}
        <UserDetailsModal
          user={showUserDetails}
          isOpen={!!showUserDetails}
          onClose={() => setShowUserDetails(null)}
          onUserUpdate={() => loadUsers(currentPage)}
        />

        {/* Individual Action Confirmation Dialog */}
        {confirmAction && (
          <ConfirmationDialog
            isOpen={showConfirmDialog}
            onClose={() => {
              setShowConfirmDialog(false);
              setConfirmAction(null);
            }}
            onConfirm={confirmAction.callback}
            title={`${
              confirmAction.type.charAt(0).toUpperCase() +
              confirmAction.type.slice(1)
            } User`}
            message={`Are you sure you want to ${confirmAction.type} ${
              confirmAction.user.full_name || confirmAction.user.email
            }?${
              confirmAction.type === "delete"
                ? " This action cannot be undone and will permanently remove all user data including documents and room memberships."
                : ""
            }`}
            confirmText={`${
              confirmAction.type.charAt(0).toUpperCase() +
              confirmAction.type.slice(1)
            } User`}
            type={
              confirmAction.type === "delete"
                ? "danger"
                : confirmAction.type === "deactivate"
                ? "warning"
                : "info"
            }
            requireReason={confirmAction.type === "deactivate"}
            loading={actionLoading === confirmAction.user.id}
          />
        )}

        {/* Bulk Confirmation Dialog */}
        {bulkOperation && (
          <BulkConfirmationDialog
            isOpen={showBulkConfirm}
            onClose={() => {
              setShowBulkConfirm(false);
              setBulkOperation(null);
            }}
            onConfirm={handleBulkOperation}
            action={bulkOperation}
            selectedCount={selectedUsers.size}
            loading={bulkLoading}
          />
        )}
      </div>
    </AdminLayout>
  );
}
