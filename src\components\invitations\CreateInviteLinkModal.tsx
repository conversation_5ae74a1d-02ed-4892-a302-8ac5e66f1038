import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import { invitationService } from "../../lib/invitationService";
import type { RoomWithDetails } from "../../lib/roomService";
import { useAuth } from "../../contexts/AuthContext";

const linkSchema = z.object({
  maxUses: z
    .number()
    .min(1, "Must allow at least 1 use")
    .max(1000, "Maximum 1000 uses")
    .optional(),
  expiresInDays: z
    .number()
    .min(1, "Must be at least 1 day")
    .max(365, "Maximum 365 days"),
  hasMaxUses: z.boolean(),
});

type LinkFormData = z.infer<typeof linkSchema>;

interface CreateInviteLinkModalProps {
  room: RoomWithDetails;
  onClose: () => void;
  onLinkCreated?: () => void;
}

export function CreateInviteLinkModal({
  room,
  onClose,
  onLinkCreated,
}: CreateInviteLinkModalProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [createdLink, setCreatedLink] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LinkFormData>({
    defaultValues: {
      maxUses: 10,
      expiresInDays: 7,
      hasMaxUses: true,
    },
  });

  const hasMaxUses = watch("hasMaxUses");

  const onSubmit = async (data: LinkFormData) => {
    if (!user) return;

    try {
      setIsLoading(true);

      const invitationLink = await invitationService.createInvitationLink({
        roomId: room.id,
        createdBy: user.id,
        maxUses: data.hasMaxUses ? data.maxUses : undefined,
        expiresInDays: data.expiresInDays,
      });

      const linkUrl = invitationService.generateInvitationUrl(
        invitationLink.link_token
      );
      setCreatedLink(linkUrl);

      toast.success("Invitation link created successfully!");
      onLinkCreated?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to create invitation link");
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async () => {
    if (!createdLink) return;

    try {
      await navigator.clipboard.writeText(createdLink);
      toast.success("Link copied to clipboard!");
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = createdLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      toast.success("Link copied to clipboard!");
    }
  };

  const shareLink = () => {
    if (!createdLink) return;

    if (navigator.share) {
      navigator.share({
        title: `Join ${room.name} on BrimBag`,
        text: `You've been invited to join the room "${room.name}" on BrimBag.`,
        url: createdLink,
      });
    } else {
      copyToClipboard();
    }
  };

  if (createdLink) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Invitation Link Created
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Share this link to invite users to "{room.name}"
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={createdLink}
                    readOnly
                    className="input-field flex-1 text-sm"
                  />
                  <button
                    onClick={copyToClipboard}
                    className="btn-secondary px-3 py-2"
                    title="Copy to clipboard"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <svg
                    className="w-5 h-5 text-blue-400 mt-0.5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <div className="text-sm text-blue-700">
                    <p className="font-medium">Important:</p>
                    <ul className="mt-1 list-disc list-inside space-y-1">
                      <li>Anyone with this link can join the room</li>
                      <li>
                        Users without accounts will be prompted to create one
                      </li>
                      <li>You can manage and deactivate this link later</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button onClick={shareLink} className="btn-primary flex-1">
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                    />
                  </svg>
                  Share Link
                </button>
                <button onClick={onClose} className="btn-secondary flex-1">
                  Done
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Create Invitation Link
            </h3>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Link expires in (days)
              </label>
              <input
                type="number"
                {...register("expiresInDays", { valueAsNumber: true })}
                disabled={isLoading}
                min="1"
                max="365"
                className="input-field"
              />
              {errors.expiresInDays && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.expiresInDays.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="checkbox"
                  {...register("hasMaxUses")}
                  disabled={isLoading}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label className="text-sm font-medium text-gray-700">
                  Limit number of uses
                </label>
              </div>

              {hasMaxUses && (
                <input
                  type="number"
                  {...register("maxUses", { valueAsNumber: true })}
                  disabled={isLoading}
                  min="1"
                  max="1000"
                  placeholder="Maximum number of uses"
                  className="input-field"
                />
              )}
              {errors.maxUses && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.maxUses.message}
                </p>
              )}
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <svg
                  className="w-5 h-5 text-yellow-400 mt-0.5 mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <div className="text-sm text-yellow-700">
                  <p className="font-medium">Security Notice:</p>
                  <p className="mt-1">
                    Anyone with this link will be able to join your room. Share
                    it only with trusted individuals.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="btn-secondary flex-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary flex-1"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </div>
                ) : (
                  "Create Link"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
