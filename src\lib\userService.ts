import { supabase } from "./supabase";

export interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: "student" | "lecturer" | "admin";
  institution: string | null;
  created_at: string;
  updated_at: string;
}

export interface UserSearchResult {
  id: string;
  email: string;
  full_name: string | null;
  role: "student" | "lecturer" | "admin";
  institution: string | null;
}

export const userService = {
  // Search users by name or email
  async searchUsers(
    query: string,
    currentUserId: string
  ): Promise<UserSearchResult[]> {
    try {
      if (!query || query.length < 2) {
        return [];
      }

      // First, let's test if we can access profiles at all
      const { error: testError } = await supabase
        .from("profiles")
        .select("id")
        .limit(1);

      if (testError) {
        console.error("Cannot access profiles table:", testError);
        throw new Error(`Database access error: ${testError.message}`);
      }

      // Try multiple search approaches for better results
      const searchTerm = query.trim();

      // Approach 1: Case-insensitive search on name and email
      let { data: profileResults, error: _profileError } = await supabase
        .from("profiles")
        .select("id, email, full_name, role, institution")
        .neq("id", currentUserId)
        .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
        .limit(10);

      if (_profileError) {
        console.error("Primary search failed:", _profileError);

        // Approach 2: Try searching with different case sensitivity
        const { data: altResults, error: altError } = await supabase
          .from("profiles")
          .select("id, email, full_name, role, institution")
          .neq("id", currentUserId)
          .or(`full_name.ilike.*${searchTerm}*,email.ilike.*${searchTerm}*`)
          .limit(10);

        if (altError) {
          console.error("Alternative search failed:", altError);

          // Approach 3: Try exact matches
          const { data: exactResults, error: exactError } = await supabase
            .from("profiles")
            .select("id, email, full_name, role, institution")
            .neq("id", currentUserId)
            .or(`email.eq.${searchTerm},full_name.eq.${searchTerm}`)
            .limit(10);

          if (exactError) {
            console.error("Exact search also failed:", exactError);
            throw new Error(`All search methods failed: ${exactError.message}`);
          }

          profileResults = exactResults;
        } else {
          profileResults = altResults;
        }
      }

      return profileResults || [];
    } catch (error: any) {
      console.error("Search users error:", error);
      throw error; // Re-throw to let the UI handle the error
    }
  },

  // Get user profile by ID
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Get user profile error:", error);
        return null;
      }

      return data;
    } catch (error: any) {
      console.error("Get user profile error:", error);
      return null;
    }
  },

  // Get multiple user profiles by IDs
  async getUserProfiles(userIds: string[]): Promise<UserProfile[]> {
    try {
      if (userIds.length === 0) return [];

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .in("id", userIds);

      if (error) {
        console.error("Get user profiles error:", error);
        return [];
      }

      return data || [];
    } catch (error: any) {
      console.error("Get user profiles error:", error);
      return [];
    }
  },

  // Check if user exists by email
  async checkUserExists(email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id")
        .eq("email", email)
        .single();

      if (error && error.code !== "PGRST116") {
        // PGRST116 = no rows returned
        console.error("Check user exists error:", error);
        return false;
      }

      return !!data;
    } catch (error: any) {
      console.error("Check user exists error:", error);
      return false;
    }
  },

  // Get user's display name
  getUserDisplayName(user: UserProfile | UserSearchResult): string {
    return user.full_name || user.email || "Unknown User";
  },

  // Get user's initials for avatar
  getUserInitials(user: UserProfile | UserSearchResult): string {
    if (user.full_name) {
      const names = user.full_name.split(" ");
      if (names.length >= 2) {
        return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
      }
      return names[0][0].toUpperCase();
    }
    return user.email?.[0]?.toUpperCase() || "U";
  },
};
