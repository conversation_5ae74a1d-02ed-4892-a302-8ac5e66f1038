# BrimBag Folder System Implementation

## Overview

This document describes the comprehensive folder structure system implemented for the BrimBag platform. The folder system allows users to organize their documents hierarchically with full drag-and-drop functionality.

## Features Implemented

### 1. Database Schema

- **New `folders` table** with hierarchical structure support
- **Updated `documents` table** with `folder_id` foreign key
- **Materialized path** for efficient folder hierarchy queries
- **Automatic path management** via database triggers
- **Circular reference prevention** with validation functions

### 2. Folder Management

- **Create folders** with custom names and descriptions
- **Nested folder support** (folders within folders)
- **Rename and delete folders** with proper validation
- **Move folders** between parent folders
- **Breadcrumb navigation** showing current folder path

### 3. Document Organization

- **Upload documents** directly to specific folders
- **Move documents** between folders via drag-and-drop
- **View documents** filtered by current folder
- **Maintain compatibility** with existing sharing features

### 4. Drag and Drop Functionality

- **HTML5 drag-and-drop** implementation without external dependencies
- **Visual feedback** during drag operations
- **Drop targets** with hover states
- **Support for both** documents and folders

### 5. User Interface

- **Folder breadcrumb** navigation component
- **Folder list** with grid and list view modes
- **Folder actions** (create, edit, delete) with modal dialogs
- **Enhanced file upload** with folder selection
- **Mobile responsive** design

## Database Migration

To add the folder system to your existing BrimBag database, run the SQL script:

```sql
-- Run this in your Supabase SQL Editor
\i database/folder-system-migration.sql
```

This script will:

- Create the `folders` table
- Add `folder_id` column to `documents` table
- Set up indexes for performance
- Create RLS policies for security
- Add database functions and triggers

## File Structure

### New Components

```
src/components/folders/
├── FolderBreadcrumb.tsx    # Navigation breadcrumb
├── FolderList.tsx          # Folder display component
├── FolderActions.tsx       # Create/edit folder forms
└── FolderModal.tsx         # Delete confirmation modal

src/hooks/
└── useDragAndDrop.ts       # Drag and drop functionality

src/lib/
└── folderService.ts        # Folder API service
```

### Updated Components

```
src/components/files/
├── FileList.tsx            # Added drag-and-drop support
└── FileUpload.tsx          # Added folder selection

src/pages/
├── Documents.tsx           # Integrated folder system
└── Upload.tsx              # Simplified to use FileUpload component

src/lib/
└── fileService.ts          # Added folder support
```

## API Methods

### Folder Service (`folderService`)

- `createFolder(data, userId)` - Create new folder
- `getUserFolders(userId)` - Get all user folders
- `getFolderById(folderId, userId)` - Get specific folder
- `getFolderHierarchy(userId, parentId)` - Get folder tree
- `getFolderBreadcrumb(folderId, userId)` - Get navigation path
- `updateFolder(folderId, data, userId)` - Update folder
- `deleteFolder(folderId, userId)` - Delete empty folder
- `moveDocumentsToFolder(documentIds, folderId, userId)` - Move documents

### Updated File Service

- `uploadDocument()` - Now accepts `folderId` parameter
- `getDocumentsByFolder(userId, folderId)` - Get documents in folder
- `moveDocumentsToFolder()` - Move documents between folders

## Testing Guide

### 1. Database Setup

1. Run the folder migration script in Supabase
2. Verify tables and policies are created correctly
3. Test basic folder CRUD operations

### 2. Folder Management

1. **Create folders**: Navigate to Documents page, click "New Folder"
2. **Nested folders**: Create a folder, navigate into it, create another folder
3. **Rename folders**: Click folder actions menu, select "Rename"
4. **Delete folders**: Try deleting folders with/without content
5. **Breadcrumb navigation**: Navigate deep into folders, use breadcrumbs to go back

### 3. Document Organization

1. **Upload to folder**: Go to Upload page, select a folder from dropdown
2. **Move documents**: Drag documents from file list to folders
3. **Folder navigation**: Click folders to view their contents
4. **Root documents**: Verify documents without folders still appear in root

### 4. Drag and Drop

1. **Document to folder**: Drag document cards onto folder cards
2. **Visual feedback**: Verify drop targets highlight on hover
3. **Folder to folder**: Drag folders to move them into other folders
4. **Prevent invalid drops**: Try dropping folder onto itself

### 5. Mobile Responsiveness

1. Test on mobile devices or browser dev tools
2. Verify touch interactions work for folder navigation
3. Check that drag-and-drop is disabled on mobile (graceful degradation)
4. Ensure folder lists and breadcrumbs are readable on small screens

### 6. Integration Testing

1. **Document sharing**: Share documents in folders, verify access
2. **Room sharing**: Share folder documents to rooms
3. **Search and filters**: Test document search within folders
4. **Statistics**: Verify document counts include folder contents

## Security Considerations

### Row Level Security (RLS)

- Users can only access their own folders
- Folder operations respect user ownership
- Document access maintains existing sharing permissions
- Basic policies to avoid complexity while ensuring security

### Validation

- Folder names validated for invalid characters
- Circular reference prevention
- Empty folder requirement for deletion
- Proper error handling throughout

## Performance Optimizations

### Database Indexes

- `idx_folders_user_id` - Fast user folder queries
- `idx_folders_parent_folder_id` - Efficient hierarchy traversal
- `idx_folders_path` - Quick path-based lookups
- `idx_documents_folder_id` - Fast document-by-folder queries

### Materialized Paths

- Folder paths stored as strings (e.g., `/folder1/subfolder2/`)
- Enables efficient ancestor/descendant queries
- Automatic path updates via database triggers

## Known Limitations

1. **Drag-and-drop on mobile**: Disabled for better touch experience
2. **Deep nesting**: No artificial limit, but UI may become unwieldy
3. **Bulk operations**: Moving many documents at once may be slow
4. **Path length**: Very long folder paths may cause display issues

## Future Enhancements

1. **Folder sharing**: Share entire folders with other users
2. **Folder templates**: Pre-defined folder structures
3. **Advanced search**: Search within specific folders
4. **Folder permissions**: Different access levels for shared folders
5. **Folder statistics**: Size and document count summaries

## Troubleshooting

### Common Issues

1. **Migration errors**: Ensure existing data is backed up before migration
2. **Permission errors**: Verify RLS policies are correctly applied
3. **Drag-and-drop not working**: Check browser compatibility and console errors
4. **Folder not appearing**: Refresh page or check network requests

### Debug Tips

1. Check browser console for JavaScript errors
2. Monitor network tab for failed API requests
3. Verify database queries in Supabase dashboard
4. Test with different user accounts to verify isolation
