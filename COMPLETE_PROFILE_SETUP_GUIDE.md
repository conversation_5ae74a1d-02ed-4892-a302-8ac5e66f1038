# Complete Profile Setup Guide

This guide will set up automatic profile creation for all users (existing and future) to ensure the user search functionality works perfectly.

## 🎯 **What This Accomplishes**

- ✅ **Migrates existing users** from `auth.users` to `profiles` table
- ✅ **Automatic profile creation** for all future users via database triggers
- ✅ **Fallback profile creation** in application code if triggers fail
- ✅ **Profile management utilities** for maintenance and debugging
- ✅ **User search functionality** will work for all users

## 📋 **Setup Steps**

### Step 1: Migrate Existing Users

Run this script in your **Supabase SQL Editor**:

```sql
-- Execute: database/migrate-existing-users-to-profiles.sql
```

**What it does:**

- Checks current state of profiles table
- Ensures table has correct structure
- Migrates all existing users from `auth.users` to `profiles`
- Shows migration results and statistics

### Step 2: Setup Automatic Profile Creation

Run this script in your **Supabase SQL Editor**:

```sql
-- Execute: database/setup-automatic-profile-creation.sql
```

**What it does:**

- Creates database function to handle new user profile creation
- Sets up triggers that fire when users register
- Creates update triggers to keep profiles in sync
- Ensures all future users automatically get profiles

### Step 3: Add Profile Management Functions

Run this script in your **Supabase SQL Editor**:

```sql
-- Execute: database/profile-management-functions.sql
```

**What it does:**

- Creates utility functions for profile management
- Adds functions to find and migrate missing users
- Provides profile statistics and cleanup functions
- Useful for ongoing maintenance

### Step 4: Application-Level Fallback

The application code has been updated to include fallback profile creation:

- **AuthContext** now ensures profiles exist when users sign in
- **ProfileService** provides utilities for profile management
- **Fallback creation** if database triggers fail for any reason

## 🧪 **Testing the Setup**

### Test 1: Check Migration Results

```sql
-- Check how many profiles were created
SELECT 'Total Profiles' as metric, COUNT(*) as count FROM profiles
UNION ALL
SELECT 'Total Auth Users', COUNT(*) FROM auth.users;

-- See sample migrated users
SELECT email, full_name, role, institution, created_at
FROM profiles
ORDER BY created_at DESC
LIMIT 10;
```

### Test 2: Test User Search

1. Go to document sharing in your app
2. Try searching for existing users by name or email
3. Check browser console for search logs:
   ```
   Searching for users with query: "john"
   Profiles table accessible, found 5 test records
   Search completed. Found 2 users matching "john"
   ```

### Test 3: Test Automatic Profile Creation

1. **Register a new user** (or have someone register)
2. **Check the profiles table**:
   ```sql
   SELECT * FROM profiles ORDER BY created_at DESC LIMIT 5;
   ```
3. **Verify the new user appears** in search results

## 🔧 **Maintenance Commands**

### Check Profile Statistics

```sql
SELECT * FROM public.get_profile_stats();
```

### Find Users Missing Profiles

```sql
SELECT * FROM public.find_users_without_profiles();
```

### Migrate Any Missing Users

```sql
SELECT public.migrate_missing_users();
```

### Clean Up Orphaned Profiles

```sql
SELECT public.cleanup_orphaned_profiles();
```

## 🚨 **Troubleshooting**

### If User Search Still Doesn't Work:

1. **Check profiles table has data**:

   ```sql
   SELECT COUNT(*) FROM profiles;
   ```

2. **Test search manually**:

   ```sql
   SELECT * FROM profiles WHERE full_name ILIKE '%test%' OR email ILIKE '%test%';
   ```

3. **Check browser console** for detailed error messages

4. **Verify RLS policies**:
   ```sql
   SELECT * FROM profiles; -- Should show all profiles
   ```

### If New Users Don't Get Profiles:

1. **Check triggers exist**:

   ```sql
   SELECT trigger_name FROM information_schema.triggers
   WHERE event_object_table = 'users' AND trigger_schema = 'auth';
   ```

2. **Test trigger function**:

   ```sql
   SELECT public.handle_new_user(); -- Should exist
   ```

3. **Check application fallback** in browser console

## ✅ **Success Indicators**

After completing all steps, you should see:

- ✅ **Profiles table populated** with all existing users
- ✅ **User search returns results** when searching by name/email
- ✅ **New users automatically get profiles** when they register
- ✅ **Console logs show successful searches** with user counts
- ✅ **Document sharing works** with user search functionality

## 📁 **Files Created/Modified**

### Database Scripts:

- `database/migrate-existing-users-to-profiles.sql`
- `database/setup-automatic-profile-creation.sql`
- `database/profile-management-functions.sql`

### Application Code:

- `src/lib/profileService.ts` (new)
- `src/contexts/AuthContext.tsx` (updated)

## 🎉 **Final Result**

Once completed, your BrimBag platform will have:

1. **Complete user search functionality** - Users can find each other by name or email
2. **Automatic profile management** - No manual intervention needed for new users
3. **Robust fallback systems** - Multiple layers ensure profiles are always created
4. **Maintenance tools** - Easy to manage and troubleshoot profiles
5. **Seamless document sharing** - Users can share documents with anyone on the platform

The user search functionality will now work perfectly for all existing and future users!
