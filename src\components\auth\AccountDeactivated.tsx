import { useNavigate } from "react-router-dom";

interface AccountDeactivatedProps {
  email: string;
  reason: string;
  deactivatedAt: string;
  onClose: () => void;
}

export function AccountDeactivated({
  email,
  reason,
  deactivatedAt,
  onClose,
}: AccountDeactivatedProps) {
  const navigate = useNavigate();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleContactSupport = () => {
    // You can customize this to your support email or contact form
    window.location.href = `mailto:<EMAIL>?subject=Account Reactivation Request&body=Hello,%0A%0A<PERSON> would like to request reactivation of my account (${email}).%0A%0ADeactivation reason: ${reason}%0ADeactivated on: ${formatDate(
      deactivatedAt
    )}%0A%0APlease let me know what steps I need to take to reactivate my account.%0A%0AThank you.`;
  };

  const handleBackToLogin = () => {
    onClose();
    navigate("/login");
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-dark-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden max-h-[90vh] flex flex-col">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-red-500 to-orange-500 p-6 text-white">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-2xl font-bold text-center mb-2">
            Account Deactivated
          </h2>
          <p className="text-white/90 text-center text-sm">
            Your account access has been temporarily suspended
          </p>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto flex-1">
          {/* Account Info */}
          <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4 mb-6">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-3">
                <span className="text-white font-medium text-sm">
                  {email.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {email}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Deactivated on {formatDate(deactivatedAt)}
                </p>
              </div>
            </div>
          </div>

          {/* Reason */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
              Reason for Deactivation
            </h3>
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-300 text-sm leading-relaxed">
                {reason}
              </p>
            </div>
          </div>

          {/* Information Box */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <svg
                className="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  What does this mean?
                </h4>
                <p className="text-blue-700 dark:text-blue-400 text-sm leading-relaxed">
                  Your account has been temporarily suspended and you cannot
                  access BrimBag services. If you believe this is an error or
                  would like to appeal this decision, please contact our support
                  team.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleContactSupport}
              className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              Contact Support
            </button>

            <button
              onClick={handleBackToLogin}
              className="w-full bg-gray-100 dark:bg-dark-700 hover:bg-gray-200 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-all duration-200"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
