# BrimBag - Room Invitation & Notification System

This document describes the newly implemented Room Invitation System and Comprehensive Notifications System for the BrimBag platform.

## Features Overview

### 🎯 Room Invitation System

#### Direct User Invitations

- **Search & Invite**: Room admins can search for existing platform users by name or email and send direct invitations
- **Personal Messages**: Optional personal messages can be included with invitations
- **Invitation Management**: View, cancel, and track the status of sent invitations
- **Accept/Decline**: Recipients can accept or decline invitations with proper notifications sent to room admins

#### Shareable Room Links

- **Generate Links**: Room admins can create shareable invitation links with customizable settings
- **Usage Limits**: Set maximum number of uses (or unlimited)
- **Expiration**: Configure expiration dates (1-365 days)
- **Link Management**: Deactivate or manage existing invitation links
- **Account Creation Flow**: Users without accounts are prompted to create one before joining

### 🔔 Comprehensive Notifications System

#### Notification Types

- **Room Invitations**: When users are invited to rooms
- **Room Activity**: New members joining, members leaving
- **Document Sharing**: Documents shared to rooms or directly with users
- **Storage Warnings**: When approaching storage limits
- **System Announcements**: Platform-wide announcements

#### Notification Features

- **Real-time Dropdown**: Bell icon in header with unread count badge
- **Notification Center**: Dedicated page for managing all notifications
- **Read/Unread Status**: Mark individual or all notifications as read
- **Persistent Storage**: Notifications persist across sessions
- **Action Integration**: Click notifications to navigate to relevant content

## Database Schema

### New Tables

#### `room_invitations`

- Tracks direct user invitations to rooms
- Includes status tracking (pending, accepted, declined, expired)
- Supports optional personal messages
- Automatic expiration after 7 days

#### `room_invitation_links`

- Manages shareable invitation links
- Configurable usage limits and expiration
- Unique token generation for security
- Usage tracking and link management

#### `notifications`

- Stores all user notifications
- Flexible JSONB data field for notification-specific information
- Read/unread status tracking
- Comprehensive notification type system

## API Services

### InvitationService (`src/lib/invitationService.ts`)

- `inviteUserToRoom()` - Send direct user invitations
- `createInvitationLink()` - Generate shareable links
- `joinRoomViaLink()` - Join room using invitation link
- `acceptInvitation()` / `declineInvitation()` - Handle invitation responses
- `getUserInvitations()` - Get user's received invitations
- `getRoomInvitations()` - Get room's sent invitations (admin)

### NotificationService (`src/lib/notificationService.ts`)

- `createNotification()` - Create new notifications
- `getUserNotifications()` - Get user's notifications
- `markAsRead()` / `markAllAsRead()` - Update read status
- `getUnreadCount()` - Get unread notification count
- Specialized notification helpers for different event types

## UI Components

### Invitation Components

- **InviteUsersModal**: Search and invite users with personal messages
- **CreateInviteLinkModal**: Generate and configure shareable links
- **InvitationsList**: Display and manage received invitations
- **JoinRoomByLink**: Public page for joining via invitation links

### Notification Components

- **NotificationsDropdown**: Header dropdown with real-time updates
- **Notifications Page**: Full notification management interface

## Integration Points

### Room Management

- Room detail pages now include "Invite Users" and "Create Link" buttons for admins
- Invitation management integrated into room settings

### Navigation

- New "Invitations" link in sidebar navigation
- Notifications accessible via header dropdown and dedicated page

### Automatic Notifications

- Room joins/leaves automatically notify room admins
- Document sharing triggers notifications to recipients
- Invitation acceptance notifies room admins

## Routes

### New Routes

- `/invitations` - Manage received room invitations
- `/notifications` - Notification center
- `/join-room/:token` - Public invitation link handler

## Security & Permissions

### Row Level Security (RLS)

- Users can only view invitations involving them
- Room admins can manage invitations for their rooms
- Users can only access their own notifications
- Invitation links have proper access controls

### Permission Checks

- Only room admins can send invitations
- Invitation link creation restricted to room admins
- Proper validation for room membership and permissions

## Mobile Responsiveness

All new UI components are fully responsive and optimized for mobile devices:

- Touch-friendly invitation interfaces
- Responsive notification dropdown
- Mobile-optimized invitation link sharing
- Proper spacing and sizing for small screens

## Installation & Setup

1. **Database Setup**: Run the SQL script in Supabase:

   ```sql
   -- Execute: database/invitation-notification-system.sql
   ```

2. **Dependencies**: All required dependencies are already included in the project

3. **Environment**: No additional environment variables required

## Usage Examples

### Inviting Users to a Room

1. Navigate to a room detail page (as admin)
2. Click "Invite Users" button
3. Search for users by name or email
4. Add optional personal message
5. Send invitations

### Creating Shareable Links

1. Navigate to a room detail page (as admin)
2. Click "Create Invite Link" button
3. Configure expiration and usage limits
4. Generate and share the link

### Managing Notifications

1. Click the bell icon in the header
2. View recent notifications in dropdown
3. Click "View all notifications" for full management
4. Mark as read or delete notifications as needed

## Future Enhancements

Potential future improvements:

- Email notifications for invitations
- Push notifications for mobile apps
- Bulk invitation management
- Advanced notification filtering
- Invitation templates
- Room invitation analytics

## Support

For issues or questions regarding the invitation and notification system, please refer to the main project documentation or create an issue in the project repository.
