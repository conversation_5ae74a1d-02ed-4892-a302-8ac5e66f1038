# Hybrid Sharing Approach

## Problem
RLS policies prevent cross-user operations needed for immediate permanent sharing.

## Solution: Hybrid Reference + Permanent Sharing

### Phase 1: Reference-Based Sharing (Immediate)
1. **Create share record** with `original_document_id` only
2. **No document copying** at share time
3. **No RLS conflicts** - just creating a share record
4. **Immediate sharing** - user sees shared document right away

### Phase 2: Convert to Permanent (On First Access)
1. **User accesses shared document** for first time
2. **System detects** it's a reference-based share
3. **Creates permanent copy** using recipient's permissions
4. **Updates share record** with `shared_document_id`
5. **Future accesses** use permanent copy

### Benefits
- ✅ **Immediate sharing** without RLS issues
- ✅ **Eventual permanence** when user accesses document
- ✅ **Backward compatible** with existing shares
- ✅ **Graceful fallback** if copying fails

### Implementation

#### Current State
- Share creation works (reference-based)
- Documents appear in shared lists
- Access uses original file (with proper RLS policies)

#### Next Steps
1. **Test current sharing** - should work now
2. **Add conversion logic** when user accesses shared documents
3. **Update UI** to show conversion status
4. **Handle edge cases** (original deleted before conversion)

### User Experience

#### Immediate (Reference Phase)
- User shares document → Recipient sees it immediately
- Recipient can access document (via original file)
- Storage quota not affected yet

#### After First Access (Permanent Phase)  
- System creates permanent copy automatically
- Recipient owns independent copy
- Storage quota updated
- Document remains accessible even if original deleted

### Technical Details

#### Share Record States
```sql
-- Reference-based (initial)
original_document_id: "abc123"
shared_document_id: null

-- Permanent (after conversion)
original_document_id: "abc123"  
shared_document_id: "def456"
```

#### Conversion Trigger Points
- Document download
- Document preview
- Document edit
- Periodic background job

### Migration Path

1. **Current shares** continue working as references
2. **New shares** start as references, convert on access
3. **Eventually** all shares become permanent
4. **Old reference logic** can be removed later

## Testing

Try sharing a document now - it should work with reference-based sharing!

The document will:
1. ✅ Share immediately (no RLS errors)
2. ✅ Appear in recipient's shared documents
3. ✅ Be accessible via original file
4. 🔄 Convert to permanent copy on first access (future enhancement)
