import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { fileService } from "../../lib/fileService";

import { useAuth } from "../../contexts/AuthContext";
import { formatDateTime } from "../../lib/utils";

interface DocumentShare {
  id: string;
  shared_with: string;
  shared_by: string;
  permission: "view" | "download";
  expires_at: string | null;
  created_at: string;
  user_profile?: {
    full_name: string | null;
    email: string;
    role: string;
  };
}

interface ShareManagementProps {
  document: DocumentFile;
  onClose: () => void;
  onSharesUpdated?: () => void;
}

export function ShareManagement({
  document,
  onClose,
  onSharesUpdated,
}: ShareManagementProps) {
  const { user } = useAuth();
  const [shares, setShares] = useState<DocumentShare[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingShares, setUpdatingShares] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadShares();
  }, [document.id]);

  const loadShares = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const documentShares = await fileService.getDocumentShares(
        document.id,
        user.id
      );
      setShares(documentShares);
    } catch (error: any) {
      console.error("Load shares error:", error);
      toast.error("Failed to load document shares");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRevokeShare = async (shareId: string, userName: string) => {
    if (!user) return;

    if (!confirm(`Are you sure you want to revoke access for ${userName}?`)) {
      return;
    }

    try {
      setUpdatingShares((prev) => new Set(prev).add(shareId));
      await fileService.revokeDocumentShare(shareId, user.id);

      setShares((prev) => prev.filter((share) => share.id !== shareId));
      toast.success(`Access revoked for ${userName}`);
      onSharesUpdated?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to revoke access");
    } finally {
      setUpdatingShares((prev) => {
        const newSet = new Set(prev);
        newSet.delete(shareId);
        return newSet;
      });
    }
  };

  const handlePermissionChange = async (
    shareId: string,
    newPermission: "view" | "download",
    userName: string
  ) => {
    if (!user) return;

    try {
      setUpdatingShares((prev) => new Set(prev).add(shareId));
      await fileService.updateDocumentSharePermission(
        shareId,
        newPermission,
        user.id
      );

      setShares((prev) =>
        prev.map((share) =>
          share.id === shareId ? { ...share, permission: newPermission } : share
        )
      );

      toast.success(`Permission updated for ${userName}`);
      onSharesUpdated?.();
    } catch (error: any) {
      toast.error(error.message || "Failed to update permission");
    } finally {
      setUpdatingShares((prev) => {
        const newSet = new Set(prev);
        newSet.delete(shareId);
        return newSet;
      });
    }
  };

  const getUserDisplayName = (share: DocumentShare): string => {
    if (share.user_profile?.full_name) {
      return share.user_profile.full_name;
    }
    return share.user_profile?.email || "Unknown User";
  };

  const getUserInitials = (share: DocumentShare): string => {
    const name = share.user_profile?.full_name;
    const email = share.user_profile?.email;

    if (name) {
      const names = name.split(" ");
      if (names.length >= 2) {
        return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
      }
      return names[0][0].toUpperCase();
    }
    return email?.[0]?.toUpperCase() || "U";
  };

  const isExpired = (expiresAt: string | null): boolean => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Manage Document Shares
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Document info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-1">{document.title}</h3>
            <p className="text-sm text-gray-600">
              {document.file_type.toUpperCase()} •{" "}
              {Math.round(document.file_size / 1024)} KB
            </p>
          </div>

          {/* Shares List */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Shared with ({shares.length})
            </h3>

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : shares.length === 0 ? (
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No shares
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  This document hasn't been shared with any users yet.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {shares.map((share) => {
                  const isUpdating = updatingShares.has(share.id);
                  const expired = isExpired(share.expires_at);

                  return (
                    <div
                      key={share.id}
                      className={`p-4 border rounded-lg ${
                        expired
                          ? "bg-red-50 border-red-200"
                          : "bg-white border-gray-200"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {/* User avatar */}
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-medium text-sm">
                              {getUserInitials(share)}
                            </span>
                          </div>

                          {/* User info */}
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {getUserDisplayName(share)}
                            </p>
                            <p className="text-sm text-gray-500">
                              {share.user_profile?.email}
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                                {share.user_profile?.role || "User"}
                              </span>
                              <span className="text-xs text-gray-500">
                                Shared {formatDateTime(share.created_at)}
                              </span>
                              {expired && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  Expired
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          {/* Permission selector */}
                          <select
                            value={share.permission}
                            onChange={(e) =>
                              handlePermissionChange(
                                share.id,
                                e.target.value as "view" | "download",
                                getUserDisplayName(share)
                              )
                            }
                            disabled={isUpdating || expired}
                            className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                          >
                            <option value="view">View Only</option>
                            <option value="download">Download</option>
                          </select>

                          {/* Revoke button */}
                          <button
                            onClick={() =>
                              handleRevokeShare(
                                share.id,
                                getUserDisplayName(share)
                              )
                            }
                            disabled={isUpdating}
                            className="text-red-600 hover:text-red-800 p-1 disabled:opacity-50"
                            title="Revoke access"
                          >
                            {isUpdating ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                            ) : (
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            )}
                          </button>
                        </div>
                      </div>

                      {/* Expiration info */}
                      {share.expires_at && (
                        <div className="mt-2 text-xs text-gray-500">
                          {expired ? "Expired" : "Expires"}{" "}
                          {formatDateTime(share.expires_at)}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Close button */}
          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
