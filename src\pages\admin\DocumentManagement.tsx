import { usePageTitle } from "../../hooks/usePageTitle";
import { AdminLayout } from "../../components/admin/layout/AdminLayout";

export function DocumentManagement() {
  usePageTitle("Document Management - Admin");

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 p-8 text-center transition-theme duration-theme">
          <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
            Document Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
            This comprehensive document oversight interface will be implemented
            in Stage 6 of the development plan.
          </p>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 transition-theme duration-theme">
            <p className="text-sm text-green-700 dark:text-green-300 transition-theme duration-theme">
              🚀 Coming soon: Document oversight, storage management, sharing
              analytics, and more!
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
