import { useState } from "react";
import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import type { DocumentFile } from "../../lib/fileService";
import { MobileFilterModal } from "./MobileFilterModal";

export interface FileFilters {
  search: string;
  fileType: string;
  sortBy: "name" | "date" | "size" | "downloads";
  sortOrder: "asc" | "desc";
  dateRange: "all" | "today" | "week" | "month" | "year";
  sizeRange: "all" | "small" | "medium" | "large";
  isPublic?: boolean | null;
  sharingStatus?: "all" | "owned" | "shared" | null;
}

interface FileFiltersProps {
  filters: FileFilters;
  onFiltersChange: (filters: FileFilters) => void;
  documents: DocumentFile[];
  viewMode: "grid" | "list";
  onViewModeChange: (mode: "grid" | "list") => void;
}

export function FileFiltersComponent({
  filters,
  onFiltersChange,
  documents,
  viewMode,
  onViewModeChange,
}: FileFiltersProps) {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  const updateFilter = (key: keyof FileFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const fileTypes = Array.from(
    new Set(documents.map((doc) => doc.file_type))
  ).sort();

  return (
    <div className="space-y-4">
      {/* Mobile Filter Button */}
      <div className="lg:hidden">
        <button
          onClick={() => setIsMobileFilterOpen(true)}
          className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
        >
          <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-500" />
          <span className="font-medium text-gray-700">Filters & Search</span>
        </button>
      </div>

      {/* Desktop Filters */}
      <div className="hidden lg:block space-y-4">
        {/* Search and View Toggle */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg
                  className="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search documents..."
                value={filters.search}
                onChange={(e) => updateFilter("search", e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => onViewModeChange("list")}
                className={`p-2 rounded ${
                  viewMode === "list"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                title="List view"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 10h16M4 14h16M4 18h16"
                  />
                </svg>
              </button>
              <button
                onClick={() => onViewModeChange("grid")}
                className={`p-2 rounded ${
                  viewMode === "grid"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                title="Grid view"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Filters Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-7 gap-4">
          {/* File Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              File Type
            </label>
            <select
              value={filters.fileType}
              onChange={(e) => updateFilter("fileType", e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="">All Types</option>
              {fileTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date Range
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => updateFilter("dateRange", e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
          </div>

          {/* Size Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              File Size
            </label>
            <select
              value={filters.sizeRange}
              onChange={(e) => updateFilter("sizeRange", e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="all">All Sizes</option>
              <option value="small">Small (&lt; 1MB)</option>
              <option value="medium">Medium (1-10MB)</option>
              <option value="large">Large (&gt; 10MB)</option>
            </select>
          </div>

          {/* Visibility Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Visibility
            </label>
            <select
              value={
                filters.isPublic === null
                  ? "all"
                  : filters.isPublic
                  ? "public"
                  : "private"
              }
              onChange={(e) => {
                const value = e.target.value;
                updateFilter(
                  "isPublic",
                  value === "all" ? null : value === "public"
                );
              }}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="all">All</option>
              <option value="private">Private</option>
              <option value="public">Public</option>
            </select>
          </div>

          {/* Sharing Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sharing Status
            </label>
            <select
              value={filters.sharingStatus || "all"}
              onChange={(e) => {
                const value = e.target.value;
                updateFilter("sharingStatus", value === "all" ? null : value);
              }}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="all">All Documents</option>
              <option value="owned">My Documents</option>
              <option value="shared">Shared with Me</option>
            </select>
          </div>

          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              value={filters.sortBy}
              onChange={(e) => updateFilter("sortBy", e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="date">Date Modified</option>
              <option value="name">Name</option>
              <option value="size">File Size</option>
              <option value="downloads">Downloads</option>
            </select>
          </div>

          {/* Sort Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Order
            </label>
            <select
              value={filters.sortOrder}
              onChange={(e) => updateFilter("sortOrder", e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>
        </div>

        {/* Active Filters Summary */}
        {(filters.search ||
          filters.fileType ||
          filters.dateRange !== "all" ||
          filters.sizeRange !== "all" ||
          filters.isPublic !== null ||
          filters.sharingStatus !== null) && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm text-gray-600">Active filters:</span>

            {filters.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{filters.search}"
                <button
                  onClick={() => updateFilter("search", "")}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            {filters.fileType && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Type: {filters.fileType}
                <button
                  onClick={() => updateFilter("fileType", "")}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            {filters.dateRange !== "all" && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Date: {filters.dateRange}
                <button
                  onClick={() => updateFilter("dateRange", "all")}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            {filters.sizeRange !== "all" && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Size: {filters.sizeRange}
                <button
                  onClick={() => updateFilter("sizeRange", "all")}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            {filters.isPublic !== null && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {filters.isPublic ? "Public" : "Private"}
                <button
                  onClick={() => updateFilter("isPublic", null)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            {filters.sharingStatus !== null && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {filters.sharingStatus === "owned"
                  ? "My Documents"
                  : "Shared with Me"}
                <button
                  onClick={() => updateFilter("sharingStatus", null)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}

            <button
              onClick={() =>
                onFiltersChange({
                  search: "",
                  fileType: "",
                  sortBy: "date",
                  sortOrder: "desc",
                  dateRange: "all",
                  sizeRange: "all",
                  isPublic: null,
                  sharingStatus: null,
                })
              }
              className="text-sm text-gray-600 hover:text-gray-800 underline"
            >
              Clear all filters
            </button>
          </div>
        )}
      </div>

      {/* Mobile Filter Modal */}
      <MobileFilterModal
        isOpen={isMobileFilterOpen}
        onClose={() => setIsMobileFilterOpen(false)}
        filters={filters}
        onFiltersChange={onFiltersChange}
        documents={documents}
        viewMode={viewMode}
        onViewModeChange={onViewModeChange}
      />
    </div>
  );
}

// Import the extended document type
import type { DocumentWithSharingInfo } from "./FileList";

// Utility function to apply filters to documents
export function applyFilters(
  documents: (DocumentFile | DocumentWithSharingInfo)[],
  filters: FileFilters,
  currentUserId?: string
): (DocumentFile | DocumentWithSharingInfo)[] {
  let filtered = [...documents];

  // Search filter
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(
      (doc) =>
        doc.title.toLowerCase().includes(searchLower) ||
        (doc.description && doc.description.toLowerCase().includes(searchLower))
    );
  }

  // File type filter
  if (filters.fileType) {
    filtered = filtered.filter((doc) => doc.file_type === filters.fileType);
  }

  // Date range filter
  if (filters.dateRange !== "all") {
    const now = new Date();
    const filterDate = new Date();

    switch (filters.dateRange) {
      case "today":
        filterDate.setHours(0, 0, 0, 0);
        break;
      case "week":
        filterDate.setDate(now.getDate() - 7);
        break;
      case "month":
        filterDate.setMonth(now.getMonth() - 1);
        break;
      case "year":
        filterDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    filtered = filtered.filter((doc) => new Date(doc.created_at) >= filterDate);
  }

  // Size range filter
  if (filters.sizeRange !== "all") {
    filtered = filtered.filter((doc) => {
      const sizeMB = doc.file_size / (1024 * 1024);
      switch (filters.sizeRange) {
        case "small":
          return sizeMB < 1;
        case "medium":
          return sizeMB >= 1 && sizeMB <= 10;
        case "large":
          return sizeMB > 10;
        default:
          return true;
      }
    });
  }

  // Visibility filter
  if (filters.isPublic !== null) {
    filtered = filtered.filter((doc) => doc.is_public === filters.isPublic);
  }

  // Sharing status filter
  if (filters.sharingStatus && currentUserId) {
    filtered = filtered.filter((doc) => {
      const docWithSharing = doc as DocumentWithSharingInfo;
      const isSharedWithMe = docWithSharing.sharing_info?.is_shared_with_me;
      const isOwnedByMe = doc.user_id === currentUserId;

      switch (filters.sharingStatus) {
        case "owned":
          return isOwnedByMe && !isSharedWithMe;
        case "shared":
          return isSharedWithMe;
        default:
          return true;
      }
    });
  }

  // Sort
  filtered.sort((a, b) => {
    let aValue: any, bValue: any;

    switch (filters.sortBy) {
      case "name":
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case "size":
        aValue = a.file_size;
        bValue = b.file_size;
        break;
      case "downloads":
        aValue = a.download_count;
        bValue = b.download_count;
        break;
      case "date":
      default:
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
        break;
    }

    if (filters.sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  return filtered;
}
