import { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import {
  FloatingActionButton,
  createUniversalFAB,
} from "./FloatingActionButton";
import { CreateRoomModal } from "../rooms/CreateRoomModal";
import { FolderModal } from "../folders/FolderModal";

export function UniversalFAB() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [showCreateFolder, setShowCreateFolder] = useState(false);

  // Don't show FAB on auth pages or if user is not authenticated
  if (
    !user ||
    location.pathname.startsWith("/login") ||
    location.pathname.startsWith("/register") ||
    location.pathname.startsWith("/forgot-password")
  ) {
    return null;
  }

  const handleUploadDocument = () => {
    navigate("/upload");
  };

  const handleCreateRoom = () => {
    setShowCreateRoom(true);
  };

  const handleCreateFolder = () => {
    setShowCreateFolder(true);
  };

  const handleSendFeedback = () => {
    navigate("/feedback");
  };

  const handleRoomCreated = () => {
    setShowCreateRoom(false);
    // Navigate to rooms page to see the newly created room
    navigate("/rooms");
  };

  const handleFolderCreated = () => {
    setShowCreateFolder(false);
    // Refresh the current page if it's the documents page
    if (location.pathname === "/documents") {
      window.location.reload();
    }
  };

  // Get current folder ID from URL search params
  const getCurrentFolderId = () => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get("folder") || null;
  };

  // Determine which actions to show based on current page
  const getActions = () => {
    const isDocumentsPage = location.pathname === "/documents";

    return createUniversalFAB({
      onUploadDocument: handleUploadDocument,
      onCreateRoom: handleCreateRoom,
      // Only show create folder on documents page
      onCreateFolder: isDocumentsPage ? handleCreateFolder : undefined,
      onSendFeedback: handleSendFeedback,
    });
  };

  return (
    <>
      <FloatingActionButton actions={getActions()} />

      {/* Create Room Modal */}
      {showCreateRoom && user && (
        <CreateRoomModal
          onClose={() => setShowCreateRoom(false)}
          onRoomCreated={handleRoomCreated}
          userId={user.id}
        />
      )}

      {/* Create Folder Modal */}
      {showCreateFolder && user && (
        <FolderModal
          isOpen={showCreateFolder}
          onClose={() => setShowCreateFolder(false)}
          folder={null}
          action="create"
          onFolderCreated={handleFolderCreated}
          onFolderDeleted={() => {}} // Not used for creation
          currentFolderId={getCurrentFolderId()} // Create in current folder
        />
      )}
    </>
  );
}
