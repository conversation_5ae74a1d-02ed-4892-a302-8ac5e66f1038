import React, { useState } from "react";
import { Worker, Viewer } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";

interface EnhancedPdfViewerProps {
  fileUrl: string;
  fileName: string;
  onError?: (error: string) => void;
}

export function EnhancedPdfViewer({ fileUrl }: EnhancedPdfViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [hasError, setHasError] = useState(false);
  const [viewerMethod, setViewerMethod] = useState<
    "react-pdf-viewer" | "iframe-fallback" | "error"
  >("react-pdf-viewer");
  const [workerUrl, setWorkerUrl] = useState<string>("");

  // List of worker URLs to try in order
  const workerUrls = [
    "https://unpkg.com/pdfjs-dist@5.3.31/build/pdf.worker.min.js",
    "https://unpkg.com/pdfjs-dist@4.8.69/build/pdf.worker.min.js",
    "https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js",
    "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.8.69/pdf.worker.min.js",
    "https://cdn.jsdelivr.net/npm/pdfjs-dist@4.8.69/build/pdf.worker.min.js",
  ];

  // Create default layout plugin with custom configuration
  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: (defaultTabs) => [
      // Keep only thumbnails and bookmarks tabs
      defaultTabs[0], // thumbnails
      defaultTabs[1], // bookmarks
    ],
    toolbarPlugin: {
      searchPlugin: {
        keyword: "",
      },
    },
  });

  // Test if a worker URL is accessible
  const testWorkerUrl = async (url: string): Promise<boolean> => {
    try {
      await fetch(url, { method: "HEAD", mode: "no-cors" });
      return true; // If no error is thrown, assume it's accessible
    } catch (_error) {
      return false;
    }
  };

  // Find the first working worker URL
  const findWorkingWorkerUrl = async (): Promise<string | null> => {
    for (const url of workerUrls) {
      const isWorking = await testWorkerUrl(url);
      if (isWorking) {
        return url;
      }
    }
    return null;
  };

  // Handle PDF viewer errors and try fallbacks
  const handlePdfViewerError = async () => {
    // Try to find a working worker URL
    const workingUrl = await findWorkingWorkerUrl();

    if (workingUrl && workingUrl !== workerUrl) {
      setWorkerUrl(workingUrl);
      setIsLoading(true);
      setHasError(false);
      return;
    }

    // If no worker URL works, fall back to iframe
    setViewerMethod("iframe-fallback");
    setIsLoading(true);
    setHasError(false);
  };

  const handleDocumentLoad = () => {
    setIsLoading(false);
    setError("");
    setHasError(false);
  };

  const handleIframeFallbackError = () => {
    const errorMessage =
      "Unable to preview this PDF file. All preview methods have failed.";
    setError(errorMessage);
    setViewerMethod("error");
    setIsLoading(false);
    setHasError(true);
  };

  // Initialize worker URL on component mount
  React.useEffect(() => {
    const initializeWorker = async () => {
      const workingUrl = await findWorkingWorkerUrl();
      if (workingUrl) {
        setWorkerUrl(workingUrl);
      } else {
        // If no worker URL works, go directly to iframe fallback
        setViewerMethod("iframe-fallback");
      }
    };

    initializeWorker();
  }, []);

  const renderViewer = () => {
    switch (viewerMethod) {
      case "react-pdf-viewer":
        if (!workerUrl) {
          return (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Initializing PDF viewer...</p>
              </div>
            </div>
          );
        }

        return (
          <Worker workerUrl={workerUrl}>
            <div className="h-full">
              <Viewer
                fileUrl={fileUrl}
                plugins={[defaultLayoutPluginInstance]}
                onDocumentLoad={handleDocumentLoad}
                renderError={() => {
                  handlePdfViewerError();
                  return (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">
                          Trying alternative PDF viewer...
                        </p>
                      </div>
                    </div>
                  );
                }}
              />
            </div>
          </Worker>
        );

      case "iframe-fallback":
        return (
          <iframe
            src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
            className="w-full h-full border-0"
            title="PDF Viewer"
            onLoad={() => {
              setIsLoading(false);
              setError("");
              setHasError(false);
            }}
            onError={handleIframeFallbackError}
          />
        );

      case "error":
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md mx-auto p-6">
              <div className="text-red-500 text-6xl mb-4">📄</div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                PDF Preview Error
              </h4>
              <p className="text-gray-600 mb-4">{error}</p>
              <p className="text-sm text-gray-500">
                Try downloading the file to view it in a PDF reader application.
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-full w-full relative bg-gray-100">
      {/* Loading Overlay */}
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">
              Loading PDF
              {viewerMethod === "iframe-fallback" ? " (fallback mode)" : ""}...
            </p>
          </div>
        </div>
      )}

      {/* Viewer Method Indicator */}
      {!error && !isLoading && (
        <div className="absolute top-2 right-2 z-20">
          <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {viewerMethod === "react-pdf-viewer"
              ? "Enhanced PDF Viewer"
              : "Basic PDF Viewer"}
          </div>
        </div>
      )}

      {/* Render the appropriate viewer */}
      {renderViewer()}
    </div>
  );
}
