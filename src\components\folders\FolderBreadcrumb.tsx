import { ChevronRightIcon, HomeIcon } from "@heroicons/react/24/outline";
import type { Folder } from "../../lib/folderService";

interface FolderBreadcrumbProps {
  currentFolder: Folder | null;
  breadcrumbPath: Folder[];
  onNavigateToFolder: (folderId: string | null) => void;
  className?: string;
}

export function FolderBreadcrumb({
  currentFolder,
  breadcrumbPath,
  onNavigateToFolder,
  className = "",
}: FolderBreadcrumbProps) {
  return (
    <nav
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-2">
        {/* Root folder (My Documents) */}
        <li>
          <button
            onClick={() => onNavigateToFolder(null)}
            className="flex items-center px-3 py-1.5 rounded-full bg-gray-100 dark:bg-dark-700 hover:bg-gray-200 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-200 font-medium"
            title="My Documents"
          >
            <HomeIcon className="h-4 w-4" />
            <span className="ml-2 hidden sm:inline">My Documents</span>
          </button>
        </li>

        {/* Breadcrumb path */}
        {breadcrumbPath.map((folder, index) => (
          <li key={folder.id} className="flex items-center">
            <ChevronRightIcon className="h-4 w-4 text-gray-400" />
            <button
              onClick={() => onNavigateToFolder(folder.id)}
              className={`px-3 py-1.5 rounded-full transition-all duration-200 truncate max-w-32 sm:max-w-48 font-medium ${
                index === breadcrumbPath.length - 1
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/40"
                  : "bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-dark-600 hover:text-gray-900 dark:hover:text-gray-100"
              }`}
              title={folder.name}
            >
              {folder.name}
            </button>
          </li>
        ))}

        {/* Current folder (if different from last breadcrumb) */}
        {currentFolder &&
          (breadcrumbPath.length === 0 ||
            breadcrumbPath[breadcrumbPath.length - 1]?.id !==
              currentFolder.id) && (
            <li className="flex items-center">
              <ChevronRightIcon className="h-4 w-4 text-gray-400" />
              <span
                className="px-3 py-1.5 rounded-full bg-blue-500 text-white font-medium truncate max-w-32 sm:max-w-48"
                title={currentFolder.name}
              >
                {currentFolder.name}
              </span>
            </li>
          )}
      </ol>
    </nav>
  );
}
